
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { 
  Camera, 
  Bird, 
  Leaf, 
  Users, 
  MapPin, 
  Clock, 
  DollarSign,
  ChevronDown,
  Filter,
  X
} from 'lucide-react';
import { SearchFilters } from '@/types/firebase';

interface AdvancedFiltersProps {
  filters: SearchFilters;
  onFiltersChange: (filters: SearchFilters) => void;
  onClear: () => void;
}

const AdvancedFilters: React.FC<AdvancedFiltersProps> = ({
  filters,
  onFiltersChange,
  onClear
}) => {
  const [openSections, setOpenSections] = useState<string[]>(['destination', 'tourType']);

  const destinations = [
    'Serengeti National Park',
    'Ngorongoro Crater',
    'Tarangire National Park',
    'Lake Manyara',
    'Ruaha National Park',
    'Selous Game Reserve',
    'Kilimanjaro National Park'
  ];

  const tourTypes = [
    { id: 'wildlife', name: 'Wildlife Safari', icon: Users },
    { id: 'kilimanjaro', name: 'Kilimanjaro Climbing', icon: MapPin },
    { id: 'cultural', name: 'Cultural Immersion', icon: Users }
  ];

  const difficulties = ['easy', 'moderate', 'challenging', 'extreme'];
  const fitnessLevels = ['low', 'moderate', 'high', 'very-high'];
  const seasons = ['dry-season', 'green-season', 'year-round'];

  const bigFive = ['Lion', 'Leopard', 'Elephant', 'Buffalo', 'Rhino'];
  const otherWildlife = [
    'Cheetah', 'Wild Dog', 'Giraffe', 'Zebra', 'Wildebeest', 
    'Hippo', 'Crocodile', 'Hyena', 'Warthog', 'Antelope'
  ];

  const activities = [
    'Game Drives', 'Walking Safari', 'Hot Air Balloon', 'Cultural Visit',
    'Photography Workshop', 'Conservation Project', 'Bird Watching',
    'Night Drive', 'Bush Breakfast', 'Sundowner'
  ];

  const toggleSection = (section: string) => {
    setOpenSections(prev => 
      prev.includes(section) 
        ? prev.filter(s => s !== section)
        : [...prev, section]
    );
  };

  const updateFilter = (key: keyof SearchFilters, value: any) => {
    onFiltersChange({ ...filters, [key]: value });
  };

  const toggleArrayFilter = (key: keyof SearchFilters, value: string) => {
    const current = (filters[key] as string[]) || [];
    const updated = current.includes(value)
      ? current.filter(item => item !== value)
      : [...current, value];
    updateFilter(key, updated);
  };

  const activeFilterCount = Object.values(filters).filter(Boolean).length;

  return (
    <Card className="w-full">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Advanced Filters
            {activeFilterCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                {activeFilterCount}
              </Badge>
            )}
          </CardTitle>
          {activeFilterCount > 0 && (
            <Button 
              variant="outline" 
              size="sm" 
              onClick={onClear}
              className="text-sm"
            >
              <X className="h-4 w-4 mr-1" />
              Clear All
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Destination Filter */}
        <Collapsible 
          open={openSections.includes('destination')}
          onOpenChange={() => toggleSection('destination')}
        >
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="w-full justify-between p-0 h-auto">
              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                <span className="font-medium">Destinations</span>
                {filters.destination && filters.destination.length > 0 && (
                  <Badge variant="secondary" className="text-xs">
                    {filters.destination.length}
                  </Badge>
                )}
              </div>
              <ChevronDown className="h-4 w-4" />
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="mt-3 space-y-2">
            {destinations.map((destination) => (
              <div key={destination} className="flex items-center space-x-2">
                <Checkbox
                  id={destination}
                  checked={filters.destination?.includes(destination) || false}
                  onCheckedChange={() => toggleArrayFilter('destination', destination)}
                />
                <label 
                  htmlFor={destination} 
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                >
                  {destination}
                </label>
              </div>
            ))}
          </CollapsibleContent>
        </Collapsible>

        {/* Tour Type Filter */}
        <Collapsible 
          open={openSections.includes('tourType')}
          onOpenChange={() => toggleSection('tourType')}
        >
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="w-full justify-between p-0 h-auto">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                <span className="font-medium">Safari Types</span>
                {filters.tourType && filters.tourType.length > 0 && (
                  <Badge variant="secondary" className="text-xs">
                    {filters.tourType.length}
                  </Badge>
                )}
              </div>
              <ChevronDown className="h-4 w-4" />
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="mt-3 space-y-2">
            {tourTypes.map((type) => (
              <div key={type.id} className="flex items-center space-x-2">
                <Checkbox
                  id={type.id}
                  checked={filters.tourType?.includes(type.id) || false}
                  onCheckedChange={() => toggleArrayFilter('tourType', type.id)}
                />
                <label 
                  htmlFor={type.id} 
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer flex items-center gap-2"
                >
                  <type.icon className="h-4 w-4" />
                  {type.name}
                </label>
              </div>
            ))}
          </CollapsibleContent>
        </Collapsible>

        {/* Price Range */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            <span className="font-medium">Price Range</span>
          </div>
          <div className="px-2">
            <Slider
              value={[filters.price?.min || 500, filters.price?.max || 5000]}
              onValueChange={([min, max]) => updateFilter('price', { min, max })}
              max={10000}
              min={500}
              step={100}
              className="w-full"
            />
            <div className="flex justify-between text-sm text-gray-600 mt-1">
              <span>${filters.price?.min || 500}</span>
              <span>${filters.price?.max || 5000}</span>
            </div>
          </div>
        </div>

        {/* Duration */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            <span className="font-medium">Duration (days)</span>
          </div>
          <div className="px-2">
            <Slider
              value={[filters.duration?.min || 1, filters.duration?.max || 14]}
              onValueChange={([min, max]) => updateFilter('duration', { min, max })}
              max={21}
              min={1}
              step={1}
              className="w-full"
            />
            <div className="flex justify-between text-sm text-gray-600 mt-1">
              <span>{filters.duration?.min || 1} day{(filters.duration?.min || 1) > 1 ? 's' : ''}</span>
              <span>{filters.duration?.max || 14} days</span>
            </div>
          </div>
        </div>

        {/* Special Features */}
        <div className="space-y-3">
          <span className="font-medium">Special Features</span>
          <div className="grid grid-cols-2 gap-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="photography"
                checked={filters.photography || false}
                onCheckedChange={(checked) => updateFilter('photography', checked)}
              />
              <label htmlFor="photography" className="text-sm flex items-center gap-1 cursor-pointer">
                <Camera className="h-4 w-4" />
                Photography Focus
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="birding"
                checked={filters.birding || false}
                onCheckedChange={(checked) => updateFilter('birding', checked)}
              />
              <label htmlFor="birding" className="text-sm flex items-center gap-1 cursor-pointer">
                <Bird className="h-4 w-4" />
                Birding Tours
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="conservation"
                checked={filters.conservation || false}
                onCheckedChange={(checked) => updateFilter('conservation', checked)}
              />
              <label htmlFor="conservation" className="text-sm flex items-center gap-1 cursor-pointer">
                <Leaf className="h-4 w-4" />
                Conservation
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="cultural"
                checked={filters.cultural || false}
                onCheckedChange={(checked) => updateFilter('cultural', checked)}
              />
              <label htmlFor="cultural" className="text-sm flex items-center gap-1 cursor-pointer">
                <Users className="h-4 w-4" />
                Cultural Experiences
              </label>
            </div>
          </div>
        </div>

        {/* Wildlife Filter */}
        <Collapsible 
          open={openSections.includes('wildlife')}
          onOpenChange={() => toggleSection('wildlife')}
        >
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="w-full justify-between p-0 h-auto">
              <span className="font-medium">Wildlife to See</span>
              <ChevronDown className="h-4 w-4" />
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="mt-3 space-y-3">
            <div>
              <p className="text-sm font-medium text-gray-700 mb-2">Big Five</p>
              <div className="grid grid-cols-2 gap-2">
                {bigFive.map((animal) => (
                  <div key={animal} className="flex items-center space-x-2">
                    <Checkbox
                      id={animal}
                      checked={filters.wildlife?.includes(animal) || false}
                      onCheckedChange={() => toggleArrayFilter('wildlife', animal)}
                    />
                    <label htmlFor={animal} className="text-sm cursor-pointer">
                      {animal}
                    </label>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-700 mb-2">Other Wildlife</p>
              <div className="grid grid-cols-2 gap-2">
                {otherWildlife.map((animal) => (
                  <div key={animal} className="flex items-center space-x-2">
                    <Checkbox
                      id={animal}
                      checked={filters.wildlife?.includes(animal) || false}
                      onCheckedChange={() => toggleArrayFilter('wildlife', animal)}
                    />
                    <label htmlFor={animal} className="text-sm cursor-pointer">
                      {animal}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>

        {/* Season Filter */}
        <div className="space-y-3">
          <span className="font-medium">Best Season</span>
          <div className="space-y-2">
            {seasons.map((season) => (
              <div key={season} className="flex items-center space-x-2">
                <Checkbox
                  id={season}
                  checked={filters.season?.includes(season) || false}
                  onCheckedChange={() => toggleArrayFilter('season', season)}
                />
                <label htmlFor={season} className="text-sm cursor-pointer capitalize">
                  {season.replace('-', ' ')}
                </label>
              </div>
            ))}
          </div>
        </div>

        {/* Difficulty Level */}
        <div className="space-y-3">
          <span className="font-medium">Difficulty Level</span>
          <Select
            value={filters.difficulty?.[0] || ''}
            onValueChange={(value) => updateFilter('difficulty', value ? [value] : [])}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select difficulty" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All levels</SelectItem>
              {difficulties.map((difficulty) => (
                <SelectItem key={difficulty} value={difficulty} className="capitalize">
                  {difficulty}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Fitness Level */}
        <div className="space-y-3">
          <span className="font-medium">Required Fitness Level</span>
          <Select
            value={filters.fitnessLevel?.[0] || ''}
            onValueChange={(value) => updateFilter('fitnessLevel', value ? [value] : [])}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select fitness level" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All levels</SelectItem>
              {fitnessLevels.map((level) => (
                <SelectItem key={level} value={level} className="capitalize">
                  {level.replace('-', ' ')}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Group Size */}
        <div className="space-y-3">
          <span className="font-medium">Maximum Group Size</span>
          <div className="px-2">
            <Slider
              value={[filters.groupSize || 12]}
              onValueChange={([size]) => updateFilter('groupSize', size)}
              max={20}
              min={2}
              step={1}
              className="w-full"
            />
            <div className="text-center text-sm text-gray-600 mt-1">
              {filters.groupSize || 12} people
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AdvancedFilters;
