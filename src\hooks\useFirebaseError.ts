/**
 * Custom hook for handling Firebase errors in React components
 * Provides consistent error handling and user-friendly messages for Warriors Safari
 */

import { useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';
import { getFirebaseErrorMessage, formatErrorForUser, getSupportInfo } from '@/utils/firebaseErrorHandler';

export interface UseFirebaseErrorOptions {
  showToast?: boolean;
  includeSupport?: boolean;
  customTitle?: string;
}

export function useFirebaseError(options: UseFirebaseErrorOptions = {}) {
  const { toast } = useToast();
  const { showToast = true, includeSupport = false, customTitle } = options;

  const handleError = useCallback((error: any, context?: string) => {
    const errorMessage = getFirebaseErrorMessage(error);
    const supportInfo = getSupportInfo(error);
    
    // Log error for debugging (only in development)
    if (process.env.NODE_ENV === 'development') {
      console.group('🔥 Firebase Error in Component');
      console.error('Error:', error);
      console.log('Context:', context);
      console.log('Custom Message:', errorMessage);
      console.groupEnd();
    }

    if (showToast) {
      const description = includeSupport && supportInfo 
        ? formatErrorForUser(error, true)
        : errorMessage;

      toast({
        title: customTitle || "Safari System Error",
        description,
        variant: "destructive",
      });
    }

    return {
      message: errorMessage,
      supportInfo,
      originalError: error
    };
  }, [toast, showToast, includeSupport, customTitle]);

  const handleAsyncOperation = useCallback(async <T>(
    operation: () => Promise<T>,
    context?: string,
    onError?: (error: any) => void
  ): Promise<T | null> => {
    try {
      return await operation();
    } catch (error) {
      const errorInfo = handleError(error, context);
      
      if (onError) {
        onError(errorInfo);
      }
      
      return null;
    }
  }, [handleError]);

  return {
    handleError,
    handleAsyncOperation
  };
}

// Specialized hooks for different Firebase operations
export function useFirebaseAuth() {
  return useFirebaseError({
    customTitle: "Authentication Error",
    includeSupport: true
  });
}

export function useFirebaseStorage() {
  return useFirebaseError({
    customTitle: "Media Upload Error",
    includeSupport: false
  });
}

export function useFirebaseFirestore() {
  return useFirebaseError({
    customTitle: "Data Access Error",
    includeSupport: false
  });
}

export function useFirebaseBooking() {
  return useFirebaseError({
    customTitle: "Booking System Error",
    includeSupport: true
  });
}
