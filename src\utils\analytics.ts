// Google Analytics utility functions
// Provides easy-to-use functions for tracking events and page views

export interface AnalyticsEvent {
  action: string;
  category: string;
  label?: string;
  value?: number;
  custom_parameters?: Record<string, any>;
}

export interface PageViewData {
  page_title?: string;
  page_location?: string;
  page_path?: string;
  content_group1?: string;
  content_group2?: string;
  custom_parameters?: Record<string, any>;
}

// Check if Google Analytics is available and user has consented
export function isAnalyticsAvailable(): boolean {
  return (
    typeof window !== 'undefined' &&
    typeof window.gtag === 'function' &&
    localStorage.getItem('analytics-consent') === 'true'
  );
}

// Track custom events
export function trackEvent(event: AnalyticsEvent): void {
  if (!isAnalyticsAvailable()) return;

  try {
    window.gtag('event', event.action, {
      event_category: event.category,
      event_label: event.label,
      value: event.value,
      ...event.custom_parameters,
    });
  } catch (error) {
    console.warn('Analytics tracking error:', error);
  }
}

// Track page views (useful for SPA navigation)
export function trackPageView(data: PageViewData = {}): void {
  if (!isAnalyticsAvailable()) return;

  try {
    window.gtag('config', import.meta.env.VITE_GA_MEASUREMENT_ID, {
      page_title: data.page_title || document.title,
      page_location: data.page_location || window.location.href,
      page_path: data.page_path || window.location.pathname,
      content_group1: data.content_group1,
      content_group2: data.content_group2,
      ...data.custom_parameters,
    });
  } catch (error) {
    console.warn('Analytics page view tracking error:', error);
  }
}

// Track user interactions
export function trackUserInteraction(action: string, element: string, details?: Record<string, any>): void {
  trackEvent({
    action,
    category: 'User Interaction',
    label: element,
    custom_parameters: details,
  });
}

// Track business events (bookings, inquiries, etc.)
export function trackBusinessEvent(action: string, details?: Record<string, any>): void {
  trackEvent({
    action,
    category: 'Business',
    custom_parameters: details,
  });
}

// Track safari-specific events
export function trackSafariEvent(action: string, safari_type?: string, details?: Record<string, any>): void {
  trackEvent({
    action,
    category: 'Safari',
    label: safari_type,
    custom_parameters: {
      safari_type,
      ...details,
    },
  });
}

// Track form submissions
export function trackFormSubmission(form_name: string, success: boolean, details?: Record<string, any>): void {
  trackEvent({
    action: success ? 'form_submit_success' : 'form_submit_error',
    category: 'Form',
    label: form_name,
    custom_parameters: details,
  });
}

// Track file downloads
export function trackDownload(file_name: string, file_type?: string): void {
  trackEvent({
    action: 'download',
    category: 'File',
    label: file_name,
    custom_parameters: {
      file_type,
      file_name,
    },
  });
}

// Track external link clicks
export function trackExternalLink(url: string, link_text?: string): void {
  trackEvent({
    action: 'click',
    category: 'External Link',
    label: url,
    custom_parameters: {
      link_text,
      destination: url,
    },
  });
}

// Track search queries
export function trackSearch(search_term: string, results_count?: number): void {
  trackEvent({
    action: 'search',
    category: 'Search',
    label: search_term,
    value: results_count,
    custom_parameters: {
      search_term,
      results_count,
    },
  });
}

// Track errors
export function trackError(error_message: string, error_type: string, page?: string): void {
  trackEvent({
    action: 'error',
    category: 'Error',
    label: error_type,
    custom_parameters: {
      error_message,
      error_type,
      page: page || window.location.pathname,
    },
  });
}

// Track performance metrics
export function trackPerformance(metric_name: string, value: number, rating: string): void {
  trackEvent({
    action: 'performance_metric',
    category: 'Performance',
    label: metric_name,
    value: Math.round(value),
    custom_parameters: {
      metric_name,
      rating,
    },
  });
}

// Enhanced ecommerce tracking for safari bookings
export function trackPurchase(transaction_id: string, value: number, currency: string = 'USD', items: any[] = []): void {
  if (!isAnalyticsAvailable()) return;

  try {
    window.gtag('event', 'purchase', {
      transaction_id,
      value,
      currency,
      items,
    });
  } catch (error) {
    console.warn('Analytics purchase tracking error:', error);
  }
}

// Track safari package views
export function trackSafariView(safari_id: string, safari_name: string, price?: number): void {
  if (!isAnalyticsAvailable()) return;

  try {
    window.gtag('event', 'view_item', {
      currency: 'USD',
      value: price,
      items: [{
        item_id: safari_id,
        item_name: safari_name,
        item_category: 'Safari Package',
        price: price,
      }],
    });
  } catch (error) {
    console.warn('Analytics safari view tracking error:', error);
  }
}

// Track when users start booking process
export function trackBeginCheckout(safari_id: string, safari_name: string, value: number): void {
  if (!isAnalyticsAvailable()) return;

  try {
    window.gtag('event', 'begin_checkout', {
      currency: 'USD',
      value,
      items: [{
        item_id: safari_id,
        item_name: safari_name,
        item_category: 'Safari Package',
        price: value,
      }],
    });
  } catch (error) {
    console.warn('Analytics begin checkout tracking error:', error);
  }
}

// Track lead generation (contact form submissions, quote requests)
export function trackLead(lead_type: string, safari_interest?: string, value?: number): void {
  trackEvent({
    action: 'generate_lead',
    category: 'Lead Generation',
    label: lead_type,
    value,
    custom_parameters: {
      lead_type,
      safari_interest,
    },
  });
}

// Debug function to check analytics status
export function getAnalyticsStatus(): { available: boolean; consent: boolean; measurementId: string | undefined } {
  return {
    available: typeof window !== 'undefined' && typeof window.gtag === 'function',
    consent: localStorage.getItem('analytics-consent') === 'true',
    measurementId: import.meta.env.VITE_GA_MEASUREMENT_ID,
  };
}
