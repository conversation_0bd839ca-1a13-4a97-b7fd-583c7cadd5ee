
import { FirebaseService } from './firebase';
import { UserProfile } from '@/types/firebase';
import { withFirebaseErrorHandling } from '@/utils/firebaseErrorHandler';

export class UserService {
  // Get user profile
  static async getUserProfile(uid: string) {
    return withFirebaseErrorHandling(async () => {
      return await FirebaseService.getUserProfile(uid);
    }, 'User Service - Get Profile');
  }

  // Update user profile
  static async updateUserProfile(uid: string, updates: Partial<UserProfile>) {
    return withFirebaseErrorHandling(async () => {
      return await FirebaseService.updateUserProfile(uid, updates);
    }, 'User Service - Update Profile');
  }

  // Create user profile
  static async createUserProfile(uid: string, email: string, displayName: string) {
    return withFirebaseErrorHandling(async () => {
      const userProfile: Partial<UserProfile> = {
        uid,
        email,
        displayName,
        role: 'user',
        loyaltyPoints: 0,
        pastBookings: [],
        preferences: {
          accommodation: 'midrange',
          activities: [],
          dietaryRestrictions: [],
          fitnessLevel: 'moderate',
          photographyInterest: false,
          birdingInterest: false
        }
      };

      return await FirebaseService.createUserProfile(uid, userProfile);
    }, 'User Service - Create Profile');
  }

  // Get user bookings
  static async getUserBookings(userId: string) {
    return withFirebaseErrorHandling(async () => {
      return await FirebaseService.getBookings(userId);
    }, 'User Service - Get Bookings');
  }

  // Get user wishlist
  static async getUserWishlist(userId: string) {
    return withFirebaseErrorHandling(async () => {
      return await FirebaseService.getUserWishlist(userId);
    }, 'User Service - Get Wishlist');
  }

  // Update user wishlist
  static async updateWishlist(userId: string, tourIds: string[], destinationIds: string[] = []) {
    return withFirebaseErrorHandling(async () => {
      return await FirebaseService.updateUserWishlist(userId, tourIds, destinationIds);
    }, 'User Service - Update Wishlist');
  }

  // Get user notifications
  static async getUserNotifications(userId: string) {
    return withFirebaseErrorHandling(async () => {
      return await FirebaseService.getUserNotifications(userId);
    }, 'User Service - Get Notifications');
  }

  // Mark notification as read
  static async markNotificationAsRead(notificationId: string) {
    return withFirebaseErrorHandling(async () => {
      return await FirebaseService.markNotificationAsRead(notificationId);
    }, 'User Service - Mark Notification Read');
  }
}
