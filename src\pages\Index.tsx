
import React from 'react';
import Header from '@/components/layout/Header';
import HeroSection from '@/components/home/<USER>';
import SafariInfoSection from '@/components/home/<USER>';

import DestinationShowcase from '@/components/home/<USER>';
import MonochromeGallery from '@/components/home/<USER>';
import TourScrollSection from '@/components/home/<USER>';

import { TestimonialsSection } from '@/components/ui/testimonials-with-marquee'; // Updated import
import Footer from '@/components/layout/Footer';
import WeatherWidget from '@/components/features/WeatherWidget';
import SustainabilityDashboard from '@/components/features/SustainabilityDashboard';
import { FlickeringGrid } from '@/components/ui/flickering-grid';

import LastSection from '@/components/home/<USER>';
import ImageSlider from '@/components/home/<USER>';

const Index = () => {

 

  return (
    <div className="min-h-screen relative bg-[#F5F1EB]">
      {/* Structured Data for Homepage */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            "name": "Warriors of Africa Safari - Premium Tanzania Wildlife Tours",
            "description": "Experience Tanzania's incredible wildlife with Warriors of Africa Safari. Book premium safari tours to Serengeti, Ngorongoro Crater, and Kilimanjaro with expert guides.",
            "url": "https://warriorsafricasafari.com/",
            "mainEntity": {
              "@type": "TravelAgency",
              "name": "Warriors of Africa Safari",
              "description": "Premium Tanzania safari tours and wildlife experiences",
              "url": "https://warriorsafricasafari.com",
              "telephone": "+25566121379",
              "email": "<EMAIL>",
              "address": {
                "@type": "PostalAddress",
                "addressLocality": "Arusha",
                "addressCountry": "Tanzania"
              },
              "hasOfferCatalog": {
                "@type": "OfferCatalog",
                "name": "Safari Tours",
                "itemListElement": [
                  {
                    "@type": "Offer",
                    "itemOffered": {
                      "@type": "TouristTrip",
                      "name": "Serengeti Safari Tours",
                      "description": "Experience the Great Migration and Big Five in Serengeti National Park"
                    }
                  },
                  {
                    "@type": "Offer",
                    "itemOffered": {
                      "@type": "TouristTrip",
                      "name": "Ngorongoro Crater Tours",
                      "description": "Explore the world's largest intact volcanic caldera"
                    }
                  },
                  {
                    "@type": "Offer",
                    "itemOffered": {
                      "@type": "TouristTrip",
                      "name": "Kilimanjaro Climbing",
                      "description": "Climb Africa's highest peak with expert guides"
                    }
                  }
                ]
              }
            },
            "breadcrumb": {
              "@type": "BreadcrumbList",
              "itemListElement": [
                {
                  "@type": "ListItem",
                  "position": 1,
                  "name": "Home",
                  "item": "https://warriorsafricasafari.com/"
                }
              ]
            }
          })
        }}
      />

      {/* Skip to main content for accessibility */}
      <a href="#main-content" className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary text-primary-foreground px-4 py-2 rounded-md z-50">
        Skip to main content
      </a>

      <Header />
      <main id="main-content" role="main">
        {/* Hero Section */}
        <section aria-label="Hero section">
          <HeroSection />
        </section>



        {/* Safari Info Section */}
        <section aria-label="Safari information and expert contact">
          <SafariInfoSection />
        </section>

        {/* Tour Scroll Section */}
        <section aria-label="Classic safari tours showcase">
          <TourScrollSection />
        </section>  

        



       



        {/* Destination Gallery */}
        <section aria-label="Tanzania destination showcase" className="relative w-full bg-[#F5F1EB] py-12 sm:py-16 md:py-20 lg:py-24">
          {/* Luxury Background Pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute inset-0" style={{
              backgroundImage: `radial-gradient(circle at 25% 25%, #4B4237 1px, transparent 1px),
                               radial-gradient(circle at 75% 75%, #4B4237 1px, transparent 1px)`,
              backgroundSize: '60px 60px'
            }}></div>
          </div>

          {/* Section Header */}
          <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-8 sm:mb-12 md:mb-16">
            <div className="text-center">
              <p className="font-sans text-[10px] xs:text-xs sm:text-sm tracking-[0.2em] sm:tracking-[0.3em] mb-3 sm:mb-4 md:mb-6 uppercase font-bold text-[#4B4237] opacity-80">
                DISCOVER TANZANIA
              </p>
              <h2 className="font-['Cormorant_Garamond'] text-4xl xs:text-5xl sm:text-6xl md:text-5xl lg:text-6xl xl:text-7xl font-normal mb-4 sm:mb-6 md:mb-8 text-[#3C352D] leading-[1.2]"
                  style={{
                    fontFamily: 'Cormorant Garamond',
                    letterSpacing: '0.02em'
                  }}>
                Stunning <em className="italic opacity-70">Destinations</em>
              </h2>
              <p className="font-sans text-sm sm:text-base md:text-lg lg:text-xl text-[#4B4237] max-w-xs sm:max-w-lg md:max-w-2xl lg:max-w-3xl mx-auto leading-relaxed px-2 sm:px-0"
                 style={{
                   lineHeight: '1.6'
                 }}>
                Journey through Tanzania's most breathtaking landscapes, from the endless plains of Serengeti to the pristine beaches of Zanzibar. Each destination offers unique adventures and unforgettable experiences.
              </p>
            </div>
          </div>

          {/* Image Slider */}
          <ImageSlider />
        </section>
       


        {/* 
        <section aria-label="Tanzania destination photos and landscapes">
          <MonochromeGallery />
        </section>*/}
                                          
        {/* Adventure Experiences Gallery */}
        <section aria-label="Tourism adventure experiences and activities">
         <LastSection />
        </section>

        

    
      </main>
      <Footer />
    </div>
  );
};

export default Index;
