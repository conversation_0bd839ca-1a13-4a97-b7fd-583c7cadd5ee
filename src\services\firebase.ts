
import { db, storage } from '@/lib/firebase';
import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDocs,
  getDoc,
  setDoc,
  query,
  where,
  orderBy,
  limit,
  Timestamp
} from 'firebase/firestore';
import {
  ref,
  uploadBytes,
  uploadBytesResumable,
  getDownloadURL,
  deleteObject
} from 'firebase/storage';
import { Tour, Review, BlogPost, Booking, ContactMessage, UserProfile, Destination, GalleryImage, CustomTourRequest } from '@/types/firebase';
import { withFirebaseErrorHandling } from '@/utils/firebaseErrorHandler';

export class FirebaseService {
  // Firebase Storage Methods
  static async uploadImage(
    file: File,
    folder: string = 'images',
    onProgress?: (progress: number) => void
  ): Promise<string> {
    return withFirebaseErrorHandling(async () => {
      // Create a unique filename
      const timestamp = Date.now();
      const fileName = `${timestamp}_${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;
      const storageRef = ref(storage, `${folder}/${fileName}`);

      // Upload with progress tracking
      const uploadTask = uploadBytesResumable(storageRef, file);

      return new Promise((resolve, reject) => {
        uploadTask.on(
          'state_changed',
          (snapshot) => {
            // Progress tracking
            const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
            if (onProgress) {
              onProgress(progress);
            }
          },
          (error) => {
            reject(error);
          },
          async () => {
            // Upload completed successfully
            try {
              const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
              resolve(downloadURL);
            } catch (error) {
              reject(error);
            }
          }
        );
      });
    }, 'Firebase Storage - Upload Image');
  }

  static async uploadMultipleImages(
    files: File[],
    folder: string = 'images',
    onProgress?: (progress: number) => void
  ): Promise<string[]> {
    return withFirebaseErrorHandling(async () => {
      const uploadPromises = files.map((file, index) =>
        this.uploadImage(file, folder, (fileProgress) => {
          if (onProgress) {
            const totalProgress = ((index * 100) + fileProgress) / files.length;
            onProgress(totalProgress);
          }
        })
      );

      return await Promise.all(uploadPromises);
    }, 'Firebase Storage - Upload Multiple Images');
  }

  static async deleteImageFromStorage(imageUrl: string): Promise<void> {
    return withFirebaseErrorHandling(async () => {
      // Extract the path from the Firebase Storage URL
      const url = new URL(imageUrl);
      const pathMatch = url.pathname.match(/\/o\/(.+)\?/);

      if (pathMatch) {
        const imagePath = decodeURIComponent(pathMatch[1]);
        const imageRef = ref(storage, imagePath);
        await deleteObject(imageRef);
      }
    }, 'Firebase Storage - Delete Image');
  }

  static validateImageFile(file: File): { isValid: boolean; error?: string } {
    // Check file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: 'Please select a valid image file (JPEG, PNG, or WebP)'
      };
    }

    // Check file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB in bytes
    if (file.size > maxSize) {
      return {
        isValid: false,
        error: 'Image size must be less than 10MB'
      };
    }

    return { isValid: true };
  }

  // Gallery Images
  static async getGalleryImages() {
    return withFirebaseErrorHandling(async () => {
      const querySnapshot = await getDocs(collection(db, 'galleryImages'));
      return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    }, 'Firestore - Get Gallery Images');
  }

  static async createGalleryImage(imageData: Omit<GalleryImage, 'id'>) {
    return withFirebaseErrorHandling(async () => {
      const docRef = await addDoc(collection(db, 'galleryImages'), {
        ...imageData,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
      return docRef.id;
    }, 'Firestore - Create Gallery Image');
  }

  static async updateGalleryImage(id: string, imageData: Partial<GalleryImage>) {
    return withFirebaseErrorHandling(async () => {
      await updateDoc(doc(db, 'galleryImages', id), {
        ...imageData,
        updatedAt: Timestamp.now()
      });
    }, 'Firestore - Update Gallery Image');
  }

  static async deleteGalleryImage(id: string) {
    return withFirebaseErrorHandling(async () => {
      await deleteDoc(doc(db, 'galleryImages', id));
    }, 'Firestore - Delete Gallery Image');
  }

  // Blog Posts
  static async getBlogPosts() {
    return withFirebaseErrorHandling(async () => {
      const querySnapshot = await getDocs(collection(db, 'blogPosts'));
      return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    }, 'Firestore - Get Blog Posts');
  }

  static async createBlogPost(postData: Omit<BlogPost, 'id'>) {
    try {
      const docRef = await addDoc(collection(db, 'blogPosts'), {
        ...postData,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
      return docRef.id;
    } catch (error) {
      console.error('Error creating blog post:', error);
      throw error;
    }
  }

  static async updateBlogPost(id: string, postData: Partial<BlogPost>) {
    try {
      await updateDoc(doc(db, 'blogPosts', id), {
        ...postData,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error updating blog post:', error);
      throw error;
    }
  }

  static async deleteBlogPost(id: string) {
    try {
      await deleteDoc(doc(db, 'blogPosts', id));
    } catch (error) {
      console.error('Error deleting blog post:', error);
      throw error;
    }
  }

  // Reviews
  static async getAllReviews() {
    return withFirebaseErrorHandling(async () => {
      const querySnapshot = await getDocs(collection(db, 'reviews'));
      return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    }, 'Firestore - Get All Reviews');
  }

  static async getReviews(tourId: string) {
    return withFirebaseErrorHandling(async () => {
      const q = query(collection(db, 'reviews'), where('tourId', '==', tourId));
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    }, 'Firestore - Get Reviews');
  }

  static async createReview(reviewData: Omit<Review, 'id'>) {
    return withFirebaseErrorHandling(async () => {
      const docRef = await addDoc(collection(db, 'reviews'), reviewData);
      return docRef.id;
    }, 'Firestore - Create Review');
  }

  static async updateReview(id: string, reviewData: Partial<Review>) {
    return withFirebaseErrorHandling(async () => {
      await updateDoc(doc(db, 'reviews', id), {
        ...reviewData,
        updatedAt: Timestamp.now()
      });
    }, 'Firestore - Update Review');
  }

  static async deleteReview(id: string) {
    return withFirebaseErrorHandling(async () => {
      await deleteDoc(doc(db, 'reviews', id));
    }, 'Firestore - Delete Review');
  }

  // Tours
  static async getTours() {
    return withFirebaseErrorHandling(async () => {
      const querySnapshot = await getDocs(collection(db, 'tours'));
      return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    }, 'Firestore - Get Tours');
  }

  static async getTour(id: string): Promise<Tour | null> {
    return withFirebaseErrorHandling(async () => {
      const docSnap = await getDoc(doc(db, 'tours', id));
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() } as Tour;
      }
      return null;
    }, 'Firestore - Get Tour');
  }

  static async createTour(tourData: Omit<Tour, 'id'>) {
    return withFirebaseErrorHandling(async () => {
      const docRef = await addDoc(collection(db, 'tours'), tourData);
      return docRef.id;
    }, 'Firestore - Create Tour');
  }

  static async updateTour(id: string, tourData: Partial<Tour>) {
    return withFirebaseErrorHandling(async () => {
      await updateDoc(doc(db, 'tours', id), {
        ...tourData,
        updatedAt: Timestamp.now()
      });
    }, 'Firestore - Update Tour');
  }

  static async deleteTour(id: string) {
    return withFirebaseErrorHandling(async () => {
      await deleteDoc(doc(db, 'tours', id));
    }, 'Firestore - Delete Tour');
  }

  static async searchTours(searchTerm: string, filters?: any) {
    return withFirebaseErrorHandling(async () => {
      let q = collection(db, 'tours');
      const querySnapshot = await getDocs(q);
      let results = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

      // Apply search term filter
      if (searchTerm) {
        results = results.filter(tour =>
          tour.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          tour.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          tour.destinations?.some((dest: string) => dest.toLowerCase().includes(searchTerm.toLowerCase()))
        );
      }

      // Apply other filters
      if (filters) {
        if (filters.category) {
          results = results.filter(tour => tour.category === filters.category);
        }
        if (filters.destination) {
          results = results.filter(tour =>
            tour.destinations?.includes(filters.destination) ||
            tour.location?.includes(filters.destination)
          );
        }
        if (filters.duration) {
          results = results.filter(tour => tour.duration?.includes(filters.duration));
        }
        if (filters.priceRange) {
          results = results.filter(tour =>
            tour.price >= filters.priceRange.min &&
            tour.price <= filters.priceRange.max
          );
        }
      }

      return results;
    }, 'Firestore - Search Tours');
  }

  // Users
  static async getAllUsers() {
    return withFirebaseErrorHandling(async () => {
      const querySnapshot = await getDocs(collection(db, 'users'));
      return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    }, 'Firestore - Get All Users');
  }

  static async getUsers() {
    return this.getAllUsers();
  }

  static async getUserProfile(uid: string) {
    return withFirebaseErrorHandling(async () => {
      const docSnap = await getDoc(doc(db, 'users', uid));
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() };
      }
      return null;
    }, 'Firestore - Get User Profile');
  }

  static async createUserProfile(uid: string, profileData: Partial<UserProfile>) {
    return withFirebaseErrorHandling(async () => {
      const userProfile: UserProfile = {
        uid,
        email: profileData.email || '',
        displayName: profileData.displayName || '',
        role: profileData.role || 'user',
        preferences: profileData.preferences || {
          accommodation: 'midrange',
          activities: [],
          dietaryRestrictions: [],
          fitnessLevel: 'moderate',
          photographyInterest: false,
          birdingInterest: false
        },
        loyaltyPoints: profileData.loyaltyPoints || 0,
        pastBookings: profileData.pastBookings || [],
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        ...profileData
      };
      await setDoc(doc(db, 'users', uid), userProfile);
      return userProfile;
    }, 'Firestore - Create User Profile');
  }

  static async updateUserProfile(uid: string, profileData: Partial<UserProfile>) {
    return withFirebaseErrorHandling(async () => {
      // First check if the document exists
      const userDoc = await getDoc(doc(db, 'users', uid));

      if (!userDoc.exists()) {
        // If document doesn't exist, create it
        return await this.createUserProfile(uid, profileData);
      } else {
        // If document exists, update it
        await updateDoc(doc(db, 'users', uid), {
          ...profileData,
          updatedAt: Timestamp.now()
        });
      }
    }, 'Firestore - Update User Profile');
  }

  static async deleteUser(uid: string) {
    try {
      await deleteDoc(doc(db, 'users', uid));
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  }

  // Bookings
  static async getAllBookings() {
    return withFirebaseErrorHandling(async () => {
      const querySnapshot = await getDocs(collection(db, 'bookings'));
      return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    }, 'Firestore - Get All Bookings');
  }

  static async getBookings(userId?: string) {
    if (userId) {
      return this.getUserBookings(userId);
    }
    return this.getAllBookings();
  }

  static async getUserBookings(userId: string) {
    return withFirebaseErrorHandling(async () => {
      const q = query(collection(db, 'bookings'), where('userId', '==', userId));
      const querySnapshot = await getDocs(q);
      const bookings = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

      // Sort by creation date (newest first)
      return bookings.sort((a, b) => {
        const dateA = a.createdAt?.toDate?.() || new Date(0);
        const dateB = b.createdAt?.toDate?.() || new Date(0);
        return dateB.getTime() - dateA.getTime();
      });
    }, 'Firestore - Get User Bookings');
  }

  static async getBooking(id: string): Promise<Booking | null> {
    return withFirebaseErrorHandling(async () => {
      const docSnap = await getDoc(doc(db, 'bookings', id));
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() } as Booking;
      }
      return null;
    }, 'Firestore - Get Booking');
  }

  static async createBooking(bookingData: Omit<Booking, 'id'>) {
    return withFirebaseErrorHandling(async () => {
      const docRef = await addDoc(collection(db, 'bookings'), {
        ...bookingData,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
      return { id: docRef.id, ...bookingData };
    }, 'Firestore - Create Booking');
  }

  static async updateBooking(id: string, bookingData: Partial<Booking>) {
    return withFirebaseErrorHandling(async () => {
      await updateDoc(doc(db, 'bookings', id), {
        ...bookingData,
        updatedAt: Timestamp.now()
      });
    }, 'Firestore - Update Booking');
  }

  // Messages
  static async getAllMessages() {
    return withFirebaseErrorHandling(async () => {
      const querySnapshot = await getDocs(collection(db, 'messages'));
      return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    }, 'Firestore - Get Messages');
  }

  static async getContactMessages() {
    return this.getAllMessages();
  }

  static async createMessage(messageData: Omit<ContactMessage, 'id'>) {
    return withFirebaseErrorHandling(async () => {
      const docRef = await addDoc(collection(db, 'messages'), messageData);
      return docRef.id;
    }, 'Firestore - Create Message');
  }

  static async createContactMessage(messageData: Omit<ContactMessage, 'id'>) {
    return withFirebaseErrorHandling(async () => {
      const docRef = await addDoc(collection(db, 'messages'), {
        ...messageData,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
      return docRef.id;
    }, 'Firestore - Create Contact Message');
  }

  static async updateContactMessage(id: string, messageData: Partial<ContactMessage>) {
    try {
      await updateDoc(doc(db, 'messages', id), {
        ...messageData,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error updating contact message:', error);
      throw error;
    }
  }



  // Destinations
  static async getDestinations() {
    try {
      const querySnapshot = await getDocs(collection(db, 'destinations'));
      return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error('Error getting destinations:', error);
      throw error;
    }
  }

  static async getDestination(id: string) {
    try {
      const docSnap = await getDoc(doc(db, 'destinations', id));
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() };
      }
      return null;
    } catch (error) {
      console.error('Error getting destination:', error);
      throw error;
    }
  }

  

  static async createDestination(destinationData: Omit<Destination, 'id'>) {
    try {
      const docRef = await addDoc(collection(db, 'destinations'), {
        ...destinationData,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
      return docRef.id;
    } catch (error) {
      console.error('Error creating destination:', error);
      throw error;
    }
  }

  static async searchDestinations(searchTerm: string) {
    try {
      const querySnapshot = await getDocs(collection(db, 'destinations'));
      let results = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

      if (searchTerm) {
        results = results.filter(destination =>
          destination.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          destination.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          destination.region?.toLowerCase().includes(searchTerm.toLowerCase())
        );
      }

      return results;
    } catch (error) {
      console.error('Error searching destinations:', error);
      throw error;
    }
  }

  // Notifications
  static async createNotification(notificationData: any) {
    try {
      const docRef = await addDoc(collection(db, 'notifications'), notificationData);
      return docRef.id;
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  }

  // Payment Transactions
  static async createPaymentTransaction(transactionData: any) {
    try {
      const docRef = await addDoc(collection(db, 'paymentTransactions'), transactionData);
      return docRef.id;
    } catch (error) {
      console.error('Error creating payment transaction:', error);
      throw error;
    }
  }

  // Custom Tour Requests
  static async createCustomTourRequest(tourRequestData: Omit<CustomTourRequest, 'id'>) {
    try {
      const docRef = await addDoc(collection(db, 'customTourRequests'), {
        ...tourRequestData,
        status: 'pending',
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
      return docRef.id;
    } catch (error) {
      console.error('Error creating custom tour request:', error);
      throw error;
    }
  }

  static async getCustomTourRequests() {
    try {
      const querySnapshot = await getDocs(collection(db, 'customTourRequests'));
      return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error('Error getting custom tour requests:', error);
      throw error;
    }
  }

  static async getCustomTourRequest(id: string) {
    try {
      const docSnap = await getDoc(doc(db, 'customTourRequests', id));
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() };
      }
      return null;
    } catch (error) {
      console.error('Error getting custom tour request:', error);
      throw error;
    }
  }

  static async updateCustomTourRequest(id: string, updateData: Partial<CustomTourRequest>) {
    try {
      await updateDoc(doc(db, 'customTourRequests', id), {
        ...updateData,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error updating custom tour request:', error);
      throw error;
    }
  }

  static async deleteCustomTourRequest(id: string) {
    try {
      await deleteDoc(doc(db, 'customTourRequests', id));
    } catch (error) {
      console.error('Error deleting custom tour request:', error);
      throw error;
    }
  }

  // Newsletter Subscriptions
  static async getNewsletterSubscriptions() {
    try {
      const querySnapshot = await getDocs(collection(db, 'newsletter_subscriptions'));
      return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error('Error getting newsletter subscriptions:', error);
      throw error;
    }
  }

  static async addNewsletterSubscription(subscriptionData: any) {
    try {
      const docRef = await addDoc(collection(db, 'newsletter_subscriptions'), subscriptionData);
      return docRef.id;
    } catch (error) {
      console.error('Error adding newsletter subscription:', error);
      throw error;
    }
  }

  static async createNewsletterSubscription(email: string, source: string = 'website') {
    try {
      const docRef = await addDoc(collection(db, 'newsletter_subscriptions'), {
        email,
        source,
        status: 'active',
        subscribedAt: Timestamp.now(),
        createdAt: Timestamp.now()
      });
      return docRef.id;
    } catch (error) {
      console.error('Error creating newsletter subscription:', error);
      throw error;
    }
  }

  static async updateNewsletterSubscription(id: string, data: any) {
    return withFirebaseErrorHandling(async () => {
      await updateDoc(doc(db, 'newsletter_subscriptions', id), {
        ...data,
        updatedAt: Timestamp.now()
      });
    }, 'Firestore - Update Newsletter Subscription');
  }

  static async deleteNewsletterSubscription(id: string) {
    return withFirebaseErrorHandling(async () => {
      await deleteDoc(doc(db, 'newsletter_subscriptions', id));
    }, 'Firestore - Delete Newsletter Subscription');
  }
}
