/* Tour Builder Luxury Enhancements */

/* Ensure dark background is always maintained - prevent white flashes */
html, body {
  background-color: #16191D !important;
  overscroll-behavior: none; /* Prevent bounce scrolling that can show white background */
}

/* Specific styling for tour builder page to prevent white background flashes */
.tour-builder-page {
  background-color: #16191D !important;
  min-height: 100vh;
  min-height: 100dvh; /* Dynamic viewport height */
  position: relative;
  /* Optimize for better scrolling performance */
  will-change: scroll-position;
  transform: translateZ(0); /* Force hardware acceleration */
  -webkit-transform: translateZ(0);
}

/* Prevent any white background during scroll animations */
.tour-builder-page::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #16191D;
  z-index: -1;
  pointer-events: none;
}

/* Ensure smooth scrolling without background flashes */
.tour-builder-page * {
  scroll-behavior: smooth;
  overscroll-behavior: contain;
}

/* Performance optimizations for scrolling */
.tour-builder-page {
  /* Enable GPU acceleration for smoother scrolling */
  -webkit-overflow-scrolling: touch;
  /* Optimize repaints during scroll */
  contain: layout style paint;
}

/* Optimize button rendering performance */
.luxury-selection-button {
  /* Use transform instead of changing layout properties */
  will-change: transform;
  /* Enable hardware acceleration */
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  /* Optimize for frequent repaints */
  contain: layout style;
}

/* Custom slider styling for luxury appearance */
.tour-builder-slider .slider-track {
  background: linear-gradient(90deg, 
    rgba(212, 194, 164, 0.2) 0%, 
    rgba(212, 194, 164, 0.4) 50%, 
    rgba(212, 194, 164, 0.2) 100%);
  height: 6px;
  border-radius: 3px;
}

.tour-builder-slider .slider-thumb {
  background: linear-gradient(135deg, #D4C2A4 0%, #C4B294 100%);
  border: 2px solid rgba(242, 238, 230, 0.3);
  box-shadow: 0 4px 12px rgba(212, 194, 164, 0.4);
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}

.tour-builder-slider .slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(212, 194, 164, 0.6);
}

/* Luxury form section animations */
@keyframes luxuryFadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.luxury-form-section {
  animation: luxuryFadeInUp 0.6s ease-out;
}

/* Enhanced luxury glass container for tour builder */
.tour-builder-glass {
  background: linear-gradient(135deg,
    rgba(212, 194, 164, 0.08) 0%,
    rgba(212, 194, 164, 0.05) 50%,
    rgba(212, 194, 164, 0.12) 100%);
  backdrop-filter: blur(30px) saturate(200%);
  -webkit-backdrop-filter: blur(30px) saturate(200%);
  border: 1px solid rgba(212, 194, 164, 0.25);
  box-shadow:
    0 32px 64px rgba(22, 25, 29, 0.5),
    0 0 0 1px rgba(212, 194, 164, 0.1),
    inset 0 1px 0 rgba(212, 194, 164, 0.2),
    inset 0 -1px 0 rgba(212, 194, 164, 0.1);
  position: relative;
  overflow: hidden;
  /* Ensure no white background shows through */
  background-color: rgba(22, 25, 29, 0.8);
  /* Prevent any potential white flash during rendering */
  will-change: auto;
  transform: translateZ(0);
}

.tour-builder-glass::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(212, 194, 164, 0.6), 
    transparent);
  animation: luxuryShimmer 4s ease-in-out infinite;
}

.tour-builder-glass::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 50% 0%, 
    rgba(212, 194, 164, 0.08) 0%, 
    transparent 60%);
  pointer-events: none;
}

/* Luxury button selection states */
.luxury-selection-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}

.luxury-selection-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(212, 194, 164, 0.2), 
    transparent);
  transition: left 0.6s ease;
}

.luxury-selection-button:hover::before {
  left: 100%;
}

.luxury-selection-button.selected {
  background: linear-gradient(135deg, 
    rgba(212, 194, 164, 0.2) 0%, 
    rgba(212, 194, 164, 0.15) 50%, 
    rgba(212, 194, 164, 0.25) 100%);
  border-color: rgba(212, 194, 164, 0.8);
  box-shadow: 
    0 8px 25px rgba(212, 194, 164, 0.3),
    inset 0 1px 0 rgba(212, 194, 164, 0.3);
}

/* Luxury separator styling */
.luxury-separator {
  position: relative;
}

.luxury-separator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(212, 194, 164, 0.3) 20%, 
    rgba(212, 194, 164, 0.6) 50%, 
    rgba(212, 194, 164, 0.3) 80%, 
    transparent 100%);
  transform: translateY(-50%);
}

/* Premium input styling */
.luxury-input {
  background: rgba(242, 238, 230, 0.05);
  border: 1px solid rgba(212, 194, 164, 0.3);
  color: #F2EEE6;
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}

.luxury-input:focus {
  background: rgba(242, 238, 230, 0.08);
  border-color: rgba(212, 194, 164, 0.8);
  box-shadow: 
    0 0 0 3px rgba(212, 194, 164, 0.2),
    0 8px 25px rgba(212, 194, 164, 0.15);
  outline: none;
}

.luxury-input::placeholder {
  color: rgba(242, 238, 230, 0.5);
}

/* Luxury submit button */
.luxury-submit-button {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #D4C2A4 0%, #C4B294 50%, #D4C2A4 100%);
  background-size: 200% 100%;
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  box-shadow: 
    0 8px 25px rgba(212, 194, 164, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.luxury-submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.3), 
    transparent);
  transition: left 0.8s ease;
}

.luxury-submit-button:hover {
  background-position: 100% 0;
  transform: translateY(-2px);
  box-shadow: 
    0 12px 35px rgba(212, 194, 164, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.luxury-submit-button:hover::before {
  left: 100%;
}

.luxury-submit-button:active {
  transform: translateY(0);
}

/* Premium shimmer animation */
@keyframes luxuryShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Luxury glow text effect */
.luxury-glow-text {
  text-shadow: 
    0 0 10px rgba(212, 194, 164, 0.3),
    0 0 20px rgba(212, 194, 164, 0.2),
    0 0 30px rgba(212, 194, 164, 0.1);
}

/* Responsive luxury enhancements */
@media (max-width: 640px) {
  .tour-builder-glass {
    backdrop-filter: blur(15px) saturate(120%);
    -webkit-backdrop-filter: blur(15px) saturate(120%);
    padding: 1rem !important;
    margin: 0.5rem;
  }

  .luxury-submit-button {
    background-size: 150% 100%;
    width: 100%;
    padding: 0.75rem 1.5rem !important;
  }

  .luxury-glow-text {
    text-shadow: 0 0 6px rgba(212, 194, 164, 0.3);
  }

  .luxury-selection-button {
    min-height: 44px; /* Touch-friendly minimum */
    font-size: 0.75rem;
  }

  .luxury-form-section {
    margin-bottom: 1.5rem;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .tour-builder-glass {
    backdrop-filter: blur(20px) saturate(150%);
    -webkit-backdrop-filter: blur(20px) saturate(150%);
  }

  .luxury-submit-button {
    background-size: 150% 100%;
  }

  .luxury-glow-text {
    text-shadow: 0 0 8px rgba(212, 194, 164, 0.3);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .tour-builder-glass {
    background: rgba(212, 194, 164, 0.15);
    border-color: rgba(212, 194, 164, 0.6);
  }
  
  .luxury-input {
    background: rgba(242, 238, 230, 0.1);
    border-color: rgba(212, 194, 164, 0.6);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .luxury-form-section,
  .tour-builder-glass::before,
  .luxury-selection-button::before,
  .luxury-submit-button::before {
    animation: none;
  }

  .luxury-submit-button:hover {
    transform: none;
  }
}

/* Mobile-specific fixes to prevent white background flashes */
@media (max-width: 640px) {
  .tour-builder-page {
    /* Prevent iOS Safari bounce scroll from showing white background */
    position: relative;
    width: 100%;
    min-height: 100vh;
    min-height: 100dvh;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: none;
    background-color: #16191D !important;
    padding: 0.5rem;
  }

  /* Ensure the main content container maintains dark background */
  .tour-builder-page > * {
    background-color: transparent;
  }

  /* Improve touch targets for mobile */
  .luxury-selection-button {
    min-height: 48px;
    padding: 0.75rem 0.5rem;
  }

  /* Better spacing for mobile forms */
  .luxury-form-section .grid {
    gap: 0.75rem;
  }

  /* Fix for potential white flash during orientation changes */
  @media (orientation: landscape) {
    .tour-builder-page {
      min-height: 100vh;
      min-height: 100dvh;
    }
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .tour-builder-page {
    /* Prevent iOS Safari bounce scroll from showing white background */
    position: fixed;
    width: 100%;
    height: 100%;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: none;
    background-color: #16191D !important;
  }

  /* Ensure the main content container maintains dark background */
  .tour-builder-page > * {
    background-color: transparent;
  }

  /* Fix for potential white flash during orientation changes */
  @media (orientation: landscape) {
    .tour-builder-page {
      height: 100vh;
      height: 100dvh;
    }
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .luxury-selection-button {
    min-height: 48px; /* Ensure touch-friendly size */
    padding: 0.75rem 0.5rem;
  }

  .luxury-selection-button:hover {
    transform: scale(0.98);
  }

  .luxury-submit-button:hover {
    transform: scale(0.98);
  }

  /* Better touch targets for inputs */
  .luxury-input {
    min-height: 44px;
    padding: 0.75rem;
  }

  /* Improve calendar touch experience */
  .tour-builder-calendar .rdp-day {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Custom scrollbar for destination list */
.destinations-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.destinations-scrollbar::-webkit-scrollbar-track {
  background: rgba(212, 194, 164, 0.1);
  border-radius: 3px;
}

.destinations-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(212, 194, 164, 0.4);
  border-radius: 3px;
}

.destinations-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(212, 194, 164, 0.6);
}

/* Calendar luxury styling overrides */
.tour-builder-calendar {
  background: #16191D;
  border: 1px solid rgba(212, 194, 164, 0.3);
  position: relative;
  z-index: 10;
}

.tour-builder-calendar .rdp {
  --rdp-cell-size: 36px;
  --rdp-accent-color: #D4C2A4;
  --rdp-background-color: #16191D;
  margin: 0;
}

.tour-builder-calendar .rdp-button {
  border: none;
  background: transparent;
  color: #F2EEE6;
  font-size: 14px;
  transition: all 0.2s ease;
}

.tour-builder-calendar .rdp-button:hover {
  background-color: rgba(212, 194, 164, 0.2);
  color: #F2EEE6;
}

.tour-builder-calendar .rdp-button:focus {
  background-color: rgba(212, 194, 164, 0.2);
  color: #F2EEE6;
  outline: none;
}

.tour-builder-calendar .rdp-day_selected {
  background-color: #D4C2A4 !important;
  color: #16191D !important;
  font-weight: 600;
}

.tour-builder-calendar .rdp-day_today {
  background-color: rgba(212, 194, 164, 0.3);
  color: #F2EEE6;
  border: 1px solid rgba(212, 194, 164, 0.5);
}

.tour-builder-calendar .rdp-day_outside {
  color: rgba(242, 238, 230, 0.3);
}

.tour-builder-calendar .rdp-day_disabled {
  color: rgba(242, 238, 230, 0.2);
  cursor: not-allowed;
}

.tour-builder-calendar .rdp-head_cell {
  color: #D4C2A4;
  font-weight: 500;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tour-builder-calendar .rdp-caption_label {
  color: #F2EEE6;
  font-weight: 600;
  font-size: 16px;
}

.tour-builder-calendar .rdp-nav_button {
  color: #F2EEE6;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.tour-builder-calendar .rdp-nav_button:hover {
  background-color: rgba(212, 194, 164, 0.2);
  color: #D4C2A4;
}

/* Calendar dropdown styling for month/year selection */
.tour-builder-calendar .rdp-caption_dropdowns {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 100;
  color: #F2EEE6

}

.tour-builder-calendar .rdp-dropdown_month,
.tour-builder-calendar .rdp-dropdown_year {
  position: relative;
  z-index: 100;
  color: #F2EEE6
}

.tour-builder-calendar .rdp-dropdown {
  appearance: none;
  background-color: #16191D;
  border: 1px solid rgba(212, 194, 164, 0.3);
  border-radius: 6px;
  color: #F2EEE6;
  font-size: 14px;
  padding: 0.5rem 2rem 0.5rem 0.75rem;
  cursor: pointer;
  position: relative;
  z-index: 100;
  min-width: 120px;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23D4C2A4' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
}

.tour-builder-calendar .rdp-dropdown:hover {
  border-color: rgba(212, 194, 164, 0.5);
  background-color: rgba(212, 194, 164, 0.05);
}

.tour-builder-calendar .rdp-dropdown:focus {
  outline: none;
  border-color: #D4C2A4;
  box-shadow: 0 0 0 2px rgba(212, 194, 164, 0.2);
}

.tour-builder-calendar .rdp-dropdown option {
  background-color: #16191D;
  color: #F2EEE6;
  padding: 0.5rem;
}

/* Ensure calendar text is always visible */
.tour-builder-calendar * {
  color: #F2EEE6
  
}

.tour-builder-calendar .rdp-day {
  color: #F2EEE6 !important;
}

.tour-builder-calendar .rdp-day:hover {
  color: #F2EEE6 !important;
}

.tour-builder-calendar .rdp-day_selected {
  color: #16191D !important;
}

/* Prevent any white backgrounds from appearing anywhere in the tour builder */
.tour-builder-page input,
.tour-builder-page textarea,
.tour-builder-page select,
.tour-builder-page button,
.tour-builder-page div,
.tour-builder-page form {
  background-color: transparent !important;
}

/* Override any potential white backgrounds from third-party components */
.tour-builder-page .bg-white,
.tour-builder-page [style*="background-color: white"],
.tour-builder-page [style*="background-color: #fff"],
.tour-builder-page [style*="background-color: #ffffff"] {
  background-color: transparent !important;
}

/* Ensure popover and dropdown backgrounds are dark */
.tour-builder-page .popover,
.tour-builder-page .dropdown,
.tour-builder-page .select-content {
  background-color: #16191D !important;
  border-color: rgba(212, 194, 164, 0.3) !important;
}

/* Fix for any potential white flash during component mounting */
.tour-builder-page * {
  transition: background-color 0s !important;
}

/* Additional responsive improvements */
@media (max-width: 480px) {
  .tour-builder-glass {
    border-radius: 1rem !important;
    margin: 0.25rem;
    padding: 0.75rem !important;
  }

  .luxury-form-section h2 {
    font-size: 1.25rem !important;
    line-height: 1.3;
  }

  .luxury-form-section p {
    font-size: 0.75rem !important;
    line-height: 1.4;
  }

  .luxury-selection-button {
    font-size: 0.7rem;
    padding: 0.5rem 0.25rem;
    min-height: 40px;
  }

  .luxury-submit-button {
    font-size: 0.875rem !important;
    padding: 0.625rem 1rem !important;
  }

  /* Improve grid layouts for very small screens */
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 0.5rem;
  }

  /* Better spacing for very small screens */
  .space-y-6 > * + * {
    margin-top: 1rem !important;
  }

  .space-y-8 > * + * {
    margin-top: 1.25rem !important;
  }
}

/* Landscape orientation optimizations for mobile */
@media (max-width: 768px) and (orientation: landscape) {
  .tour-builder-page {
    padding: 0.5rem;
  }

  .tour-builder-glass {
    max-height: calc(100vh - 1rem);
    overflow-y: auto;
  }

  .luxury-form-section {
    margin-bottom: 1rem;
  }
}
