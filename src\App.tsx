
import React, { Suspense, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom';
import { AuthProvider } from '@/contexts/AuthContext';
import { LanguageProvider } from '@/contexts/LanguageContext';
import { WishlistProvider } from '@/contexts/WishlistContext';
import { Toaster } from '@/components/ui/toaster';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import PageLoader from '@/components/ui/PageLoader';
import ErrorBoundary, { PageErrorBoundary } from '@/components/ui/error-boundary';
import SkipLinks from '@/components/ui/skip-links';
import { useAnalytics } from '@/hooks/useAnalytics';

// Critical pages - loaded immediately
import Index from '@/pages/Index';
import NotFound from '@/pages/NotFound';



// Lazy-loaded pages for better performance
const Tours = React.lazy(() => import('@/pages/Tours'));
const TourDetail = React.lazy(() => import('@/pages/TourDetail'));
const About = React.lazy(() => import('@/pages/About'));
const Contact = React.lazy(() => import('@/pages/Contact'));
const Gallery = React.lazy(() => import('@/pages/Gallery'));
const Blog = React.lazy(() => import('@/pages/Blog'));
const BlogPost = React.lazy(() => import('@/pages/BlogPost'));
const Reviews = React.lazy(() => import('@/pages/Reviews'));
const UserDashboard = React.lazy(() => import('@/pages/UserDashboard'));
const AdminDashboard = React.lazy(() => import('@/pages/admin/AdminDashboard'));
const Booking = React.lazy(() => import('@/pages/Booking'));
const TourBuilder = React.lazy(() => import('@/pages/TourBuilder'));
const Register = React.lazy(() => import('@/pages/Register'));
const Login = React.lazy(() => import('@/pages/Login'));
const Destinations = React.lazy(() => import('@/pages/Destinations'));
const DestinationDetail = React.lazy(() => import('@/pages/DestinationDetail'));
const TravelResources = React.lazy(() => import('@/pages/TravelResources'));
const EnhancedTours = React.lazy(() => import('@/pages/EnhancedTours'));
const Privacy = React.lazy(() => import('@/pages/Privacy'));
const Terms = React.lazy(() => import('@/pages/Terms'));
const Cookies = React.lazy(() => import('@/pages/Cookies'));

// Lazy-loaded components
const CookieConsent = React.lazy(() => import('@/components/features/CookieConsent'));
const ChatBotWidget = React.lazy(() => import('@/components/features/ChatBotWidget'));
const OnboardingWrapper = React.lazy(() => import('@/components/user/OnboardingWrapper'));

import ScrollToTop from '@/components/utils/ScrollToTop';

// Component to provide context-aware loading
const ContextAwareLoader = () => {
  const location = useLocation();
  const isDarkPage = location.pathname === '/tour-builder';

  return (
    <PageLoader
      title="Loading Page..."
      subtitle="Please wait while we prepare your safari experience..."
      darkTheme={isDarkPage}
    />
  );
};

// Analytics wrapper component
const AnalyticsWrapper = ({ children }: { children: React.ReactNode }) => {
  const { trackAppError } = useAnalytics();

  useEffect(() => {
    // Track app initialization
    console.log('Warriors of Africa Safari app initialized');

    // Set up global error tracking
    const handleError = (event: ErrorEvent) => {
      trackAppError(event.message, 'javascript_error', window.location.pathname);
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      trackAppError(String(event.reason), 'promise_rejection', window.location.pathname);
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, [trackAppError]);

  return <>{children}</>;
};

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      staleTime: 5 * 60 * 1000,
    },
  },
});

function App() {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <LanguageProvider>
          <WishlistProvider>
            <AuthProvider>
              <Router>
                <AnalyticsWrapper>
                  <SkipLinks />
                  <ScrollToTop />
                  <div className="min-h-screen bg-background">
                    <PageErrorBoundary>
                      <Suspense fallback={<ContextAwareLoader />}>
                        <main id="main-content">
                        <Routes>
                          <Route path="/" element={<Index />} />
                          <Route path="/tours" element={<Tours />} />
                          <Route path="/tours/luxury" element={<Tours />} />
                          <Route path="/tours/budget" element={<Tours />} />
                          <Route path="/tours/family" element={<Tours />} />
                          <Route path="/tours/photography" element={<Tours />} />
                          <Route path="/tours/cultural" element={<Tours />} />
                          <Route path="/tours/climbing" element={<Tours />} />
                          <Route path="/tours/:id" element={<TourDetail />} />
                          <Route path="/book/:tourId" element={<Booking />} />
                          <Route path="/tour-builder" element={<TourBuilder />} />
                          <Route path="/about" element={<About />} />
                          <Route path="/contact" element={<Contact />} />
                          <Route path="/gallery" element={<Gallery />} />
                          <Route path="/blog" element={<Blog />} />
                          <Route path="/blog/:slug" element={<BlogPost />} />
                          <Route path="/reviews" element={<Reviews />} />
                          <Route path="/user-dashboard" element={
                            <ProtectedRoute>
                              <UserDashboard />
                            </ProtectedRoute>
                          } />
                          <Route path="/admin/*" element={
                            <ProtectedRoute adminOnly={true}>
                              <AdminDashboard />
                            </ProtectedRoute>
                          } />
                          <Route path="/register" element={<Register />} />
                          <Route path="/login" element={<Login />} />
                          <Route path="/destinations" element={<Destinations />} />
                          <Route path="/destinations/:id" element={<DestinationDetail />} />
                          <Route path="/travel-resources" element={<TravelResources />} />
                          <Route path="/enhanced-tours" element={<EnhancedTours />} />
                          <Route path="/privacy" element={<Privacy />} />
                          <Route path="/terms" element={<Terms />} />
                          <Route path="/cookies" element={<Cookies />} />

                          <Route path="/dashboard" element={
                            <ProtectedRoute>
                              <UserDashboard />
                            </ProtectedRoute>
                          } />
                          <Route path="*" element={<NotFound />} />
                        </Routes>
                      </main>
                    </Suspense>
                  </PageErrorBoundary>

                  {/* Lazy load non-critical components */}
                  <Suspense fallback={null}>
                    <CookieConsent />
                    <ChatBotWidget />
                    <OnboardingWrapper />
                  </Suspense>

                    <Toaster />
                  </div>
                </AnalyticsWrapper>
              </Router>
            </AuthProvider>
          </WishlistProvider>
        </LanguageProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;
