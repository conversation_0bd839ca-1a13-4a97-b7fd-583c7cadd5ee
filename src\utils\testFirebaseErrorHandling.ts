/**
 * Test utilities for Firebase error handling
 * Use these functions to verify that Firebase errors are properly masked
 */

import { handleFirebaseError, withFirebaseErrorHandling, getFirebaseErrorMessage } from './firebaseErrorHandler';

// Mock Firebase errors for testing
export const mockFirebaseErrors = {
  authUserNotFound: {
    code: 'auth/user-not-found',
    message: 'Firebase: There is no user record corresponding to this identifier.'
  },
  authWrongPassword: {
    code: 'auth/wrong-password',
    message: 'Firebase: The password is invalid or the user does not have a password.'
  },
  firestorePermissionDenied: {
    code: 'firestore/permission-denied',
    message: 'Missing or insufficient permissions.'
  },
  storageQuotaExceeded: {
    code: 'storage/quota-exceeded',
    message: 'Quota for bucket exceeded.'
  },
  networkError: {
    code: 'network-request-failed',
    message: 'A network error (such as timeout, interrupted connection or unreachable host) has occurred.'
  }
};

// Test function to verify error handling
export function testFirebaseErrorHandling() {
  console.group('🧪 Testing Firebase Error Handling');
  
  Object.entries(mockFirebaseErrors).forEach(([errorType, mockError]) => {
    console.group(`Testing ${errorType}`);
    
    const customError = handleFirebaseError(mockError, `Test - ${errorType}`);
    
    console.log('Original Error:', mockError);
    console.log('Custom Message:', customError.customMessage);
    console.log('Error Code:', customError.errorCode);
    console.log('Category:', customError.category);
    
    // Verify the error message is user-friendly
    const isUserFriendly = !customError.customMessage.toLowerCase().includes('firebase') &&
                          !customError.customMessage.includes(mockError.code) &&
                          customError.customMessage.includes('safari');
    
    console.log('✅ User-friendly:', isUserFriendly);
    console.groupEnd();
  });
  
  console.groupEnd();
}

// Test async error handling wrapper
export async function testAsyncErrorHandling() {
  console.group('🧪 Testing Async Error Handling');
  
  try {
    await withFirebaseErrorHandling(async () => {
      throw mockFirebaseErrors.authUserNotFound;
    }, 'Test Async Operation');
  } catch (error) {
    console.log('Caught custom error:', error.message);
    console.log('Original error preserved:', error.originalError);
  }
  
  console.groupEnd();
}

// Verify all error codes have custom messages
export function verifyErrorCoverage() {
  console.group('🧪 Verifying Error Coverage');
  
  const testCodes = [
    'auth/user-not-found',
    'auth/wrong-password',
    'auth/email-already-in-use',
    'firestore/permission-denied',
    'firestore/not-found',
    'storage/object-not-found',
    'storage/quota-exceeded',
    'functions/not-found',
    'network-request-failed'
  ];
  
  testCodes.forEach(code => {
    const mockError = { code, message: `Mock ${code} error` };
    const customMessage = getFirebaseErrorMessage(mockError);
    
    const hasCustomMessage = !customMessage.includes('technical difficulties') &&
                            customMessage.includes('safari');
    
    console.log(`${code}: ${hasCustomMessage ? '✅' : '❌'} ${customMessage}`);
  });
  
  console.groupEnd();
}

// Run all tests
export function runAllErrorHandlingTests() {
  console.log('🚀 Running Firebase Error Handling Tests...');
  
  testFirebaseErrorHandling();
  verifyErrorCoverage();
  testAsyncErrorHandling();
  
  console.log('✅ All Firebase error handling tests completed!');
}
