
import { FirebaseService } from './firebase';
import { withFirebaseErrorHandling } from '@/utils/firebaseErrorHandler';

export class ContentService {
  // Blog posts
  static async getBlogPosts(published: boolean = true) {
    return withFirebaseErrorHandling(async () => {
      return await FirebaseService.getBlogPosts(published);
    }, 'Content Service - Get Blog Posts');
  }

  // Travel guides
  static async getTravelGuides(category?: string) {
    return withFirebaseErrorHandling(async () => {
      return await FirebaseService.getTravelGuides(category);
    }, 'Content Service - Get Travel Guides');
  }

  // Destinations
  static async getDestinations() {
    return withFirebaseErrorHandling(async () => {
      return await FirebaseService.getDestinations();
    }, 'Content Service - Get Destinations');
  }

  // Reviews
  static async getReviews(tourId?: string) {
    return withFirebaseErrorHandling(async () => {
      return await FirebaseService.getReviews(tourId);
    }, 'Content Service - Get Reviews');
  }

  // Wildlife sightings
  static async getWildlifeSightings(destinationId?: string) {
    try {
      return await FirebaseService.getWildlifeSightings(destinationId);
    } catch (error) {
      console.error('Error getting wildlife sightings:', error);
      throw error;
    }
  }

  // Weather data
  static async getWeatherData(destinationId: string) {
    try {
      return await FirebaseService.getWeatherData(destinationId);
    } catch (error) {
      console.error('Error getting weather data:', error);
      throw error;
    }
  }

  // Activities
  static async getActivities() {
    try {
      return await FirebaseService.getActivities();
    } catch (error) {
      console.error('Error getting activities:', error);
      throw error;
    }
  }

  // Accommodations
  static async getAccommodations() {
    try {
      return await FirebaseService.getAccommodations();
    } catch (error) {
      console.error('Error getting accommodations:', error);
      throw error;
    }
  }
}
