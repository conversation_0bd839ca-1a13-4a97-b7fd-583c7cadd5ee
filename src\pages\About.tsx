

import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

// Register the GSAP plugin
gsap.registerPlugin(ScrollTrigger);

const About = () => {
  const mainRef = useRef(null);

  useEffect(() => {
    // GSAP context for scoped animations and automatic cleanup
    const ctx = gsap.context(() => {
     
      // --- BANNER ANIMATIONS ---
      gsap.to("#bannerBigimg", {
        duration: 2, yPercent: 150, ease: "power2.inOut",
        yoyo: true, repeat: 1, repeatRefresh: true
      });
      gsap.to("#headingBig", {
        duration: 1.5, scale: 2, delay: 0.8, transformOrigin: "top left",
        ease: "power2.inOut", yoyo: true, repeat: 1, repeatRefresh: true
      });
      const tl = gsap.timeline();
      tl.to("#headingSmall, #headingText", { delay: 1.2, opacity: 0 })
        .to("#headingSmall, #headingText", { left: "-100vw" })
        .to("#headingSmall, #headingText", { delay: 1.4, duration: 1.8, left: 0, opacity: 1 });

      // --- COMPANY SECTION ---
      gsap.from(".company-section .title, #compDescription", {
        opacity: 0, y: -200, duration: 1, ease: "power2.inOut", stagger: 0.2,
        scrollTrigger: { trigger: ".company-section", start: "top 70%" }
      });
      gsap.from(".compy-box", {
        duration: 1.6, opacity: 0, scale: 0, transformOrigin: "top right", ease: "power2.inOut",
        scrollTrigger: { trigger: ".company-section", start: "top 70%" }
      });

      // --- DESIGNER SECTION ---
      gsap.from(".designer-section .title, #designDescription", {
        y: -200, opacity: 0, duration: 1.6, ease: "power2.inOut", stagger: 0.2,
        scrollTrigger: { trigger: ".designer-section", start: "top 70%" }
      });
      gsap.from(".box-border", {
        duration: 1.5, opacity: 0, scale: 0.6, rotate: 15, ease: "power.inOut",
        scrollTrigger: { trigger: ".designer-section", start: "top 70%" }
      });
      gsap.from(".team1, .team3", {
        duration: 1.6, delay: 0.2, opacity: 0, rotate: 45, scale: 0.5, ease: "power2.inOut", transformOrigin: "top",
        scrollTrigger: { trigger: ".designer-section", start: "top 70%" }
      });
      gsap.from(".team2", {
        duration: 1.6, delay: 0.2, opacity: 0, rotate: -45, scale: 0.5, ease: "power2.inOut", transformOrigin: "top",
        scrollTrigger: { trigger: ".designer-section", start: "top 70%" }
      });
     
      // --- PROJECTS SECTION ---
      gsap.from(".projects-section .title", {
        y: -200, opacity: 0, duration: 1.6, ease: "power2.inOut",
        scrollTrigger: { trigger: ".projects-section", start: "top 70%" }
      });
      gsap.from(".pbox-left", {
        duration: 1.6, x: -800, opacity: 0, ease: "power2.inOut", stagger: 0.2,
        scrollTrigger: { trigger: ".projects-section", start: "top 70%" }
      });
      gsap.from(".pbox-right", {
        duration: 1.6, x: 500, opacity: 0, ease: "power2.inOut", stagger: 0.2,
        scrollTrigger: { trigger: ".projects-section", start: "top 70%" }
      });
      gsap.from(".project-content", {
        duration: 1.6, x: -200, y: -200, opacity: 0, delay: 0.2, ease: "power2.inOut",
        scrollTrigger: { trigger: ".projects-section", start: "top 70%" }
      });

      // --- TESTIMONIALS SECTION ---
      gsap.from(".testimonial-section .title", {
        y: -200, opacity: 0, duration: 1.6, ease: "power2.inOut",
        scrollTrigger: { trigger: ".testimonial-section", start: "top 70%" }
      });
      gsap.from(".left-row", {
        duration: 1.6, opacity: 0, xPercent: -100, ease: "power2.inOut",
        scrollTrigger: { trigger: ".testimonial-section", start: "top 70%" }
      });
      gsap.from(".right-row", {
        duration: 1.6, opacity: 0, xPercent: 100, ease: "power2.inOut",
        scrollTrigger: { trigger: ".testimonial-section", start: "top 60%" }
      });

      // --- CONTACT SECTION ---
      gsap.from(".contact-box, .contact-border", {
        duration: 1.6, opacity: 0, scale: 0, transformOrigin: "top right", ease: "power2.inOut", stagger: 0.2,
        scrollTrigger: { trigger: ".contact-us", start: "top 70%" }
      });

    }, mainRef);

    // Cleanup function to kill all animations and ScrollTriggers on component unmount
    return () => ctx.revert();
  }, []);


  // Shared Title Component
  const SectionTitle = ({ children, dark }: { children: React.ReactNode; dark?: boolean }) => (
    <h2 className={`title absolute top-[-40px] left-0 w-full text-center text-4xl md:text-6xl lg:text-7xl xl:text-8xl font-light leading-none uppercase select-none pointer-events-none font-cormorant tracking-widest z-80 ${dark ? 'text-[#D4C2A4]/30' : 'text-[#D4C2A4]/25'}`}>
      {children}
    </h2>
  );

  return (
    <div className=" bg-[#16191D]">
      <Header />
      <div ref={mainRef}>
      {/* Banner Start */}
           {/* Banner Start */}
      <section className="banner bg-[#16191D] min-h-screen w-full overflow-hidden relative flex items-center">
        {/* Background Image - more personal and evocative */}
        <div className="absolute top-0 left-0 w-full h-full z-0">
          <img 
            src="https://firebasestorage.googleapis.com/v0/b/warriorsofafricasafari-5bb0d.firebasestorage.app/o/imagez%2Fballoon.jpg?alt=media&token=7d76c7b2-2bc1-4f7b-95b5-4bb7be186afd" 
            className="w-full h-full object-cover object-center" 
            alt="A safari guide looking over the African savannah" 
          />
        </div>

        {/* Gradient Overlay for Readability */}
        <div className="absolute inset-0 bg-gradient-to-r from-[#16191D]/90 via-[#16191D]/60 to-transparent z-10"></div>

        <div className="container mx-auto max-w-7xl h-full flex items-center relative z-20">
          <div className="banner-text p-10 md:p-14 lg:p-16 w-full max-w-3xl">
            {/* Page Title */}
            <h4 
              className="text-[#D4C2A4] font-open-sans uppercase tracking-widest text-sm md:text-base mb-4 animate-fade-in-up" 
              style={{ animationDelay: '0.2s' }}
            >
             
            </h4>
            
            {/* Main Headline */}
            <h1 
              className="text-[#F2EEE6] text-5xl md:text-7xl lg:text-8xl font-light leading-tight mb-6 select-none font-cormorant tracking-wide drop-shadow-2xl animate-fade-in-up"
              style={{ animationDelay: '0.5s' }}
            >
              A Legacy Born<br /> from the <span className="italic luxury-glow-text text-[#D4C2A4]">Wild</span>.
            </h1>

            {/* Introductory Paragraph */}
            <p 
              className="text-base md:text-lg text-[#F2EEE6]/80 font-light leading-relaxed font-open-sans tracking-wide max-w-xl mb-12 animate-fade-in-up" 
              style={{ animationDelay: '0.8s' }}
            >
              Warriors of Africa Safari is more than a company—it's a promise. A promise to share the profound, untamed beauty of our home and to forge a legacy of conservation, connection, and unforgettable adventure.
            </p>

            {/* Scroll Down Indicator */}
            <a 
              
              className="inline-flex items-center gap-3 text-[#D4C2A4] no-underline transition-opacity duration-300 hover:opacity-80 animate-fade-in-up"
              style={{ animationDelay: '1.1s' }}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M19 13l-7 7-7-7m14-8l-7 7-7-7"></path></svg>
              <span className="font-open-sans text-sm uppercase tracking-wider">Discover Our Roots</span>
            </a>
          </div>
        </div>
      </section>
      {/* Banner End */}

     
      {/* Banner End */}



      {/* Company Start */}
      <section className="company-section relative bg-[#16191D] overflow-hidden pt-16 md:pt-40 lg:pt-52 z-0">
        <SectionTitle>Our Story</SectionTitle>
        <div className="container mx-auto max-w-7xl px-3">
          <div className="flex flex-col md:flex-row items-stretch">
            <div className="w-full md:w-1/2 self-center pb-4 md:pb-5 lg:pb-0">
              <div className="py-0 lg:py-5" id="compDescription">
                <div className="paragraph pb-4 md:pb-3 text-[#F2EEE6]/90 text-xs md:text-base lg:text-lg leading-relaxed lg:leading-8">
                  <p className="mb-4 lg:mb-6 text-[16px] font-open-sans font-light tracking-wide"><span className="text-4xl lg:text-5xl font-cormorant font-light text-[#D4C2A4] float-left mr-2 mt-1 leading-none">W</span>arriors of Africa Safari was born from a deep passion for Africa's untamed wilderness and a commitment to sharing its breathtaking beauty with adventurous souls from around the world.</p>
                  <p className="mb-4 lg:mb-6 text-[16px] font-open-sans font-light tracking-wide">Our mission is to provide authentic, transformative safari experiences that connect travelers with Africa's magnificent wildlife, diverse cultures, and stunning landscapes. We believe that every safari should be a journey of discovery, conservation, and personal growth, creating memories that last a lifetime while supporting local communities and wildlife preservation.</p>
                </div>
                <a href="/tours" className="inline-flex items-center justify-center gap-3 bg-gradient-to-r from-[#D4C2A4] to-[#D4C2A4]/80 hover:from-[#D4C2A4]/90 hover:to-[#D4C2A4]/70 text-[#16191D] uppercase no-underline relative z-10 transition-all duration-300 select-none font-semibold tracking-wider
                  py-3 px-6 md:py-4 lg:py-5 md:px-8 lg:px-10 text-xs md:text-sm lg:text-base font-open-sans
                  before:content-[''] before:absolute before:top-1 before:left-1 lg:before:top-1.5 lg:before:left-1.5 before:w-full before:h-full before:border-2 before:border-[#D4C2A4]/40 before:z-[-1] before:transition-all before:duration-300
                  hover:before:top-0 hover:before:left-0 hover:shadow-xl hover:shadow-[#D4C2A4]/25 hover:scale-105 active:scale-95">
                  read more <img src="https://www.yudiz.com/codepen/interior-design/arrow-right.svg" className="w-4 lg:w-6" alt="Arrow" />
                </a>
              </div>
            </div>
            <div className="w-full md:w-1/2">
              <div className="compy-box mt-4 md:mt-0 relative mr-4 md:mr-0 before:content-[''] before:absolute before:top-[-16px] before:left-4 md:before:top-[-24px] md:before:left-6 before:w-full before:h-full before:border-2 before:border-[#D4C2A4]/60 before:z-10 shadow-xl">
                <img src="https://firebasestorage.googleapis.com/v0/b/warriorsofafricasafari-5bb0d.firebasestorage.app/o/imagez%2Fphoto%20(5).webp?alt=media&token=24f45f21-a393-4d34-8d80-2c966ca280a8" className="w-full h-full object-cover" alt="Safari Wildlife" />
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* Company End */}

      {/* Designer Start */}
      <section className="designer-section relative bg-[#16191D] pt-28 md:pt-36 lg:pt-52">
        <SectionTitle dark>Our Team</SectionTitle>
        <div className="container mx-auto max-w-7xl px-3">
          <div className="flex flex-col md:flex-row items-stretch gap-x-4">
            <div className="w-full md:w-1/2">
              <div className="designer-box relative w-full h-[300px] md:h-[410px] lg:h-[605px] z-10 max-w-md mx-auto md:mx-0">
                <div className="team-member team1 absolute w-[152px] md:w-[190px] lg:w-[260px] xl:w-[315px] aspect-square top-[-22%] left-[10%] lg:left-[4%] z-20">
                  <img src="https://images.unsplash.com/photo-1516426122078-c23e76319801" className="w-full h-full object-cover" alt="Safari Guide" />
                </div>
                <div className="team-member team2 absolute w-[152px] md:w-[190px] lg:w-[260px] xl:w-[315px] aspect-square top-[12%] lg:top-[17%] right-[6%]">
                  <img src="https://images.unsplash.com/photo-1516426122078-c23e76319801" className="w-full h-full object-cover" alt="Wildlife Expert" />
                </div>
                <div className="team-member team3 absolute w-[152px] md:w-[190px] lg:w-[260px] xl:w-[315px] aspect-square bottom-0 left-[8%] z-20">
                  <img src="https://images.unsplash.com/photo-1516426122078-c23e76319801" className="w-full h-full object-cover" alt="Conservation Specialist" />
                </div>
                <div className="box-border absolute top-0 left-0 border-2 border-[#D4C2A4]/60 h-[88%] w-[90%] z-[-1] shadow-2xl"></div>
              </div>
            </div>
            <div className="w-full md:w-1/2 self-center">
              <div className="py-4 lg:py-5" id="designDescription">
                <div className="paragraph pb-3 text-[#F2EEE6]/90 text-xs md:text-base lg:text-lg leading-relaxed lg:leading-8">
                  <p className="mb-4 lg:mb-6 font-open-sans font-light tracking-wide"><span className="text-4xl lg:text-5xl font-cormorant font-light text-[#D4C2A4] float-left mr-2 mt-1 leading-none">O</span>ur team consists of experienced safari guides, wildlife experts, and conservation specialists who are passionate about Africa's natural heritage. With decades of combined experience in the field, our guides possess intimate knowledge of animal behavior, local ecosystems, and cultural traditions, ensuring every safari is both educational and unforgettable.</p>
                </div>
                <a href="/tours" className="inline-flex items-center justify-center gap-3 bg-gradient-to-r from-[#D4C2A4] to-[#D4C2A4]/80 hover:from-[#D4C2A4]/90 hover:to-[#D4C2A4]/70 text-[#16191D] uppercase no-underline relative z-10 transition-all duration-300 select-none font-semibold tracking-wider
                  py-3 px-6 md:py-4 lg:py-5 md:px-8 lg:px-10 text-xs md:text-sm lg:text-base font-open-sans
                  before:content-[''] before:absolute before:top-1 before:left-1 lg:before:top-1.5 lg:before:left-1.5 before:w-full before:h-full before:border-2 before:border-[#D4C2A4]/40 before:z-[-1] before:transition-all before:duration-300
                  hover:before:top-0 hover:before:left-0 hover:shadow-xl hover:shadow-[#D4C2A4]/25 hover:scale-105 active:scale-95">
                  read more <img src="https://www.yudiz.com/codepen/interior-design/arrow-right.svg" className="w-4 lg:w-6" alt="Arrow" />
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* Designer End */}
     
      {/* Projects Start */}
      <section className="projects-section relative bg-[#16191D] overflow-hidden pt-16 md:pt-28 lg:pt-52 z-0">
        <SectionTitle>Experiences</SectionTitle>
        <div className="container mx-auto max-w-7xl px-3">
          <div className="flex flex-col md:flex-row gap-1 md:gap-3 lg:gap-4">
            <div className="w-full md:w-9/12">
              <div className="grid grid-cols-12 gap-1 md:gap-3 lg:gap-4">
                <div className="col-span-8">
                  <div className="project-box pbox-left relative w-full h-[158px] md:h-[195px] lg:h-[334px]">
                    <img src="https://images.unsplash.com/photo-1516426122078-c23e76319801" className="absolute w-full h-full object-cover" alt="Big Five Safari" />
                  </div>
                </div>
                <div className="col-span-4">
                  <div className="project-box pbox-right relative w-full h-[158px] md:h-[195px] lg:h-[334px]">
                    <img src="https://firebasestorage.googleapis.com/v0/b/warriorsofafricasafari-5bb0d.firebasestorage.app/o/imagez%2Fexperience%20(5).webp?alt=media&token=543dd6fc-2df2-4ec1-894c-fb1ccb8690f2" className="absolute w-full h-full object-cover" alt="Cultural Experience" />
                  </div>
                </div>
                <div className="col-span-4">
                  <div className="project-box pbox-left relative w-full h-[158px] md:h-[195px] lg:h-[334px]">
                    <img src="https://firebasestorage.googleapis.com/v0/b/warriorsofafricasafari-5bb0d.firebasestorage.app/o/imagez%2Fexperience%20(4).webp?alt=media&token=32a5be35-a7c7-470a-9936-525a4269e86c" className="absolute w-full h-full object-cover" alt="Walking Safari" />
                  </div>
                </div>
                <div className="col-span-8">
                  <div className="project-content relative w-full h-[158px] md:h-[195px] lg:h-[334px] p-2 md:p-5 lg:p-8 z-10 flex items-center
                    before:content-[''] before:absolute before:h-[2px] before:w-[100px] lg:before:w-[130px] before:bg-[#D4C2A4]/60 before:top-4 before:left-1/2 before:-translate-x-1/2
                    after:content-[''] after:absolute after:h-[2px] after:w-[100px] lg:after:w-[130px] after:bg-[#D4C2A4]/60 after:bottom-4 after:right-4">
                    <img src="https://firebasestorage.googleapis.com/v0/b/warriorsofafricasafari-5bb0d.firebasestorage.app/o/imagez%2Fexperience%20(2).webp?alt=media&token=82c9f803-739e-4600-b28c-611b25b3a88f" className="absolute top-0 left-0 w-full h-full z-[-1]" alt="Safari Sunset" />
                    <div className="w-10/12 md:w-8/12 2xl:w-7/12 ml-auto">
                      <p className="text-[10px] lg:text-base leading-tight lg:leading-relaxed mb-2 lg:mb-2 font-open-sans font-light tracking-wide text-[#F2EEE6]/95">From thrilling game drives to intimate walking safaris, from cultural immersions to conservation experiences, we offer diverse adventures that showcase Africa's incredible biodiversity and rich heritage. Each journey is carefully crafted to create meaningful connections between our guests and the wild heart of Africa.</p>
                      <a href="/tours" className="common-btn ml-auto table p-1 md:p-2 lg:p-2.5 bg-[#D4C2A4]/20 hover:bg-[#D4C2A4]/30 rounded-lg transition-all duration-300">
                        <img src="https://www.yudiz.com/codepen/interior-design/arrow-right.svg" className="w-3 md:w-4 lg:w-5" alt="Arrow" />
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="w-full md:w-3/12">
              <div className="project-box pbox-right relative h-[158px] md:h-full">
                <img src="https://firebasestorage.googleapis.com/v0/b/warriorsofafricasafari-5bb0d.firebasestorage.app/o/imagez%2Fexperience%20(1).webp?alt=media&token=07f4a871-e077-427c-8539-14467d2a50ed" className="absolute w-full h-full object-cover" alt="Night Safari" />
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* Projects End */}

      {/* Testimonials Start */}
      <section className="testimonial-section relative bg-[#16191D] overflow-hidden pt-20 md:pt-32 lg:pt-44 z-0">
        <SectionTitle>Testimonials</SectionTitle>
        <div className="container mx-auto max-w-7xl px-3 space-y-8 md:space-y-0">
          <div className="left-row flex flex-col md:flex-row items-end">
            <div className="w-full md:w-1/2 order-2 md:order-1">
              <div className="content border-2 border-[#D4C2A4]/40 p-5 pt-12 md:p-5 lg:p-10 border-r-0 bg-[#D4C2A4]/5 shadow-lg">
                <h3 className="font-light text-lg lg:text-xl text-[#F2EEE6] mb-3 lg:mb-5 font-cormorant tracking-wide">Sarah Mitchell</h3>
                <p className="italic text-xs md:text-sm lg:text-base text-[#F2EEE6]/80 leading-relaxed font-open-sans font-light tracking-wide">"Warriors of Africa Safari exceeded all my expectations. The guides were incredibly knowledgeable, and seeing the Big Five in their natural habitat was absolutely breathtaking. This was truly the adventure of a lifetime, and I can't wait to return!"</p>
              </div>
            </div>
            <div className="w-1/2 md:w-1/2 order-1 md:order-2 -mb-10 md:mb-0 ml-auto md:ml-0">
              <div className="img-box w-full max-w-[145px] md:max-w-[250px] lg:max-w-[340px] aspect-square overflow-hidden">
                <img src="https://firebasestorage.googleapis.com/v0/b/warriorsofafricasafari-5bb0d.firebasestorage.app/o/imagez%2Fgoldplate.225Z.png?alt=media&token=376b59c3-b341-4aea-93fa-5463c1e27099" className="w-full h-full object-cover" alt="Safari Guest" />
              </div>
            </div>
          </div>
          <div className="right-row flex flex-col md:flex-row items-start">
            <div className="w-1/2 md:w-1/2 -mt-10 md:mt-0 mr-auto md:mr-0">
              <div className="img-box w-full max-w-[145px] md:max-w-[250px] lg:max-w-[340px] aspect-square overflow-hidden ml-auto">
                <img src="https://firebasestorage.googleapis.com/v0/b/warriorsofafricasafari-5bb0d.firebasestorage.app/o/imagez%2Fgoldplate.225Z.png?alt=media&token=376b59c3-b341-4aea-93fa-5463c1e27099" className="w-full h-full object-cover" alt="Safari Guest" />
              </div>
            </div>
            <div className="w-full md:w-1/2">
              <div className="content border-2 border-[#D4C2A4]/40 p-5 pb-12 md:p-5 lg:p-10 border-l-0 bg-[#D4C2A4]/5 shadow-lg">
                <h3 className="font-light text-lg lg:text-xl text-[#F2EEE6] mb-3 lg:mb-5 font-cormorant tracking-wide">David Thompson</h3>
                <p className="italic text-xs md:text-sm lg:text-base text-[#F2EEE6]/80 leading-relaxed font-open-sans font-light tracking-wide">"The cultural immersion aspect of our safari was incredible. Meeting local communities and learning about their traditions while experiencing Africa's wildlife was deeply moving. Warriors of Africa Safari creates authentic connections that go beyond just wildlife viewing."</p>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* Testimonials End */}
     
      {/* Contact Start */}
      <section className="contact-us relative overflow-hidden py-10 md:py-20 lg:py-24 bg-[#16191D]">
        <div className="container mx-auto max-w-7xl px-3">
          <div className="flex flex-col md:flex-row">
            <div className="w-full md:w-5/12 mb-6 md:mb-0">
              <h4 className="font-light text-xl md:text-2xl lg:text-3xl text-[#F2EEE6] mb-6 md:mb-8 lg:mb-10 font-cormorant tracking-wide">Warriors of Africa Safari</h4>
              <ul className="flex flex-col w-full lg:w-4/5">
                <li className="relative pl-9 md:pl-12 lg:pl-16 mb-4 lg:mb-6">
                  <img src="https://www.yudiz.com/codepen/interior-design/location.svg" className="absolute top-0 left-0 w-6 lg:w-8" alt="Location" />
                  <a className="text-sm md:text-base lg:text-lg text-[#F2EEE6]/80 leading-snug lg:leading-7 no-underline font-light tracking-wide" href="#">
                    Arusha Tanzania, East Africa
                  </a>
                </li>
                <li className="relative pl-9 md:pl-12 lg:pl-16 mb-4 lg:mb-6">
                  <img src="https://www.yudiz.com/codepen/interior-design/email.svg" className="absolute top-0 left-0 w-6 lg:w-8" alt="Email" />
                  <a className="text-sm md:text-base lg:text-lg text-[#F2EEE6]/80 leading-snug lg:leading-7 font-light tracking-wide" href="mailto:<EMAIL>"><EMAIL></a>
                </li>
                <li className="relative pl-9 md:pl-12 lg:pl-16">
                  <img src="https://www.yudiz.com/codepen/interior-design/call.svg" className="absolute top-0 left-0 w-6 lg:w-8" alt="Call" />
                  <a className="text-sm md:text-base lg:text-lg text-[#F2EEE6]/80 leading-snug lg:leading-7 font-light tracking-wide" href="tel:+25566121379">+25566121379</a>
                </li>
              </ul>
            </div>
            <div className="w-full md:w-7/12">
              <div className="contact-info relative z-10 p-8 md:p-12 lg:p-14 mr-3 md:mr-0">
                <div className="contact-box absolute top-0 left-0 w-full h-full bg-[#D4C2A4]/5 z-[-2] shadow-xl"></div>
                <div className="contact-border absolute top-[-12px] left-3 md:top-[-18px] lg:top-[-24px] md:left-4 lg:left-6 w-full h-full border-2 border-[#D4C2A4]/40 z-[-1]"></div>
                <h3 className="font-light text-3xl md:text-4xl lg:text-5xl text-[#F2EEE6] uppercase leading-tight lg:leading-snug mb-4 lg:mb-6 font-cormorant tracking-wider">Join Our Adventure !</h3>
                <p className="text-sm md:text-base lg:text-base text-[#F2EEE6]/80 leading-relaxed mb-8 lg:mb-12 font-open-sans font-light tracking-wide">Ready to embark on the safari of a lifetime? Contact us to plan your African adventure and discover the untamed beauty of the wild. Let us create memories that will last forever.</p>
                <a href="/tours" className="inline-flex items-center justify-center gap-3 bg-gradient-to-r from-[#D4C2A4] to-[#D4C2A4]/80 hover:from-[#D4C2A4]/90 hover:to-[#D4C2A4]/70 text-[#16191D] uppercase no-underline relative z-10 transition-all duration-300 select-none font-semibold tracking-wider
                  py-3 px-6 md:py-4 lg:py-5 md:px-8 lg:px-10 text-xs md:text-sm lg:text-base font-open-sans
                  before:content-[''] before:absolute before:top-1 before:left-1 lg:before:top-1.5 lg:before:left-1.5 before:w-full before:h-full before:border-2 before:border-[#D4C2A4]/40 before:z-[-1] before:transition-all before:duration-300
                  hover:before:top-0 hover:before:left-0 hover:shadow-xl hover:shadow-[#D4C2A4]/25 hover:scale-105 active:scale-95">
                  Book Safari <img src="https://www.yudiz.com/codepen/interior-design/arrow-right.svg" className="w-4 lg:w-6" alt="Arrow" />
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* Contact End */}
      </div>
      <Footer />
    </div>
  );
};

export default About