
import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import TourCard from '@/components/tours/TourCard';
import TourFilters from '@/components/tours/TourFilters';
import PageLoader from '@/components/ui/PageLoader';
import { Button } from '@/components/ui/button';
import { Grid, List, Search, Loader2, Filter, SortAsc, X } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { useTours } from '@/hooks/useTours';
import { useToast } from '@/hooks/use-toast';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';

const Tours = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const { toast } = useToast();
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  // Filter state
  const [filters, setFilters] = useState({
    tourType: 'all',
    accommodationLevel: 'all',
    priceRange: [0, 10000],
    duration: 'all',
    destinations: []
  });

  const { tours, loading, error } = useTours();

  // Get filter from URL path (for footer links like /tours/luxury)
  const urlFilter = window.location.pathname.split('/tours/')[1];

  // Get tourType filter from URL parameters (new filtering system)
  const tourTypeFilter = searchParams.get('tourType');

  // Sync URL tourType parameter with filter state
  useEffect(() => {
    if (tourTypeFilter && tourTypeFilter !== filters.tourType) {
      setFilters(prev => ({ ...prev, tourType: tourTypeFilter }));
    }
  }, [tourTypeFilter]);

  // Initialize search from URL parameters
  useEffect(() => {
    const urlDestination = searchParams.get('destination');
    const urlSearch = searchParams.get('search');
    const urlDate = searchParams.get('date');
    const urlTravelers = searchParams.get('travelers');

    if (urlDestination || urlSearch) {
      const searchValue = urlDestination || urlSearch || '';
      setSearchTerm(searchValue);

      // Show welcome message for search from hero
      if (urlDestination) {
        const dateText = urlDate ? ` on ${new Date(urlDate).toLocaleDateString()}` : '';
        const travelersText = urlTravelers ? ` for ${urlTravelers} travelers` : '';

        toast({
          title: "Search Applied",
          description: `Showing tours in ${urlDestination}${dateText}${travelersText}`,
        });
      }
    }
  }, [searchParams, toast]);

  // Filter tours based on search term, URL filter, tourType parameter, and filter state
  const filteredTours = tours.filter(tour => {
    const matchesSearch = searchTerm === '' ||
      tour.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      tour.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      tour.category.toLowerCase().includes(searchTerm.toLowerCase());

    // Apply tourType filter (new system from URL params)
    let matchesTourType = true;
    if (tourTypeFilter) {
      matchesTourType = tour.tourType === tourTypeFilter;
    }

    // Apply filter state tourType
    let matchesFilterTourType = true;
    if (filters.tourType !== 'all') {
      matchesFilterTourType = tour.tourType === filters.tourType;
    }

    // Apply accommodation level filter
    let matchesAccommodation = true;
    if (filters.accommodationLevel !== 'all') {
      matchesAccommodation = tour.accommodationLevel?.toLowerCase() === filters.accommodationLevel.toLowerCase();
    }

    // Apply price range filter
    const matchesPrice = tour.price >= filters.priceRange[0] && tour.price <= filters.priceRange[1];

    // Apply duration filter
    let matchesDuration = true;
    if (filters.duration !== 'all') {
      const tourDurationDays = parseInt(tour.duration.match(/\d+/)?.[0] || '0');
      switch (filters.duration) {
        case '1-3 days':
          matchesDuration = tourDurationDays >= 1 && tourDurationDays <= 3;
          break;
        case '4-7 days':
          matchesDuration = tourDurationDays >= 4 && tourDurationDays <= 7;
          break;
        case '8+ days':
          matchesDuration = tourDurationDays >= 8;
          break;
      }
    }

    // Apply destinations filter
    let matchesDestinations = true;
    if (filters.destinations.length > 0) {
      matchesDestinations = filters.destinations.some(dest =>
        tour.destinations.some(tourDest =>
          tourDest.toLowerCase().includes(dest.toLowerCase())
        )
      );
    }

    // Apply URL-based filter for legacy footer links
    let matchesUrlFilter = true;
    if (urlFilter) {
      switch (urlFilter) {
        case 'luxury':
          matchesUrlFilter = tour.accommodationLevel?.toLowerCase() === 'luxury';
          break;
        case 'budget':
          matchesUrlFilter = tour.accommodationLevel?.toLowerCase() === 'budget';
          break;
        case 'family':
          matchesUrlFilter = tour.category?.toLowerCase().includes('family') ||
                           tour.title.toLowerCase().includes('family');
          break;
        case 'photography':
          matchesUrlFilter = tour.category?.toLowerCase() === 'photography' ||
                           tour.title.toLowerCase().includes('photography');
          break;
        case 'cultural':
          matchesUrlFilter = tour.category?.toLowerCase() === 'cultural' ||
                           tour.title.toLowerCase().includes('cultural');
          break;
        case 'climbing':
          matchesUrlFilter = tour.title.toLowerCase().includes('kilimanjaro') ||
                           tour.title.toLowerCase().includes('climbing') ||
                           tour.category?.toLowerCase().includes('climbing');
          break;
        default:
          matchesUrlFilter = true;
      }
    }

    return matchesSearch && matchesTourType && matchesFilterTourType &&
           matchesAccommodation && matchesPrice && matchesDuration &&
           matchesDestinations && matchesUrlFilter;
  });

  if (loading) {
    return (
      <PageLoader
        title="Loading Tours..."
        subtitle="Preparing your safari adventure options..."
      />
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-[#16191D]">
        <Header />
        <main className="">
          <div className="bg-gradient-to-br from-[#16191D] via-[#1a1e23] to-[#16191D] text-[#F2EEE6] py-16 sm:py-20 md:py-32 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-[#D4C2A4]/5 to-transparent"></div>
            <div className="container mx-auto px-4 sm:px-6 relative z-10">
              <h1 className="text-3xl sm:text-4xl md:text-6xl lg:text-7xl font-cormorant font-light mb-4 sm:mb-6 tracking-wide">Safari Tours</h1>
              <p className="text-base sm:text-lg md:text-xl font-open-sans text-[#D4C2A4] max-w-2xl leading-relaxed">
                Discover our carefully curated collection of safari experiences in Tanzania
              </p>
            </div>
          </div>
          <div className="container mx-auto px-4 sm:px-6 py-12 sm:py-16 flex items-center justify-center min-h-[300px] sm:min-h-[400px]">
            <div className="text-center bg-[#1a1e23] p-6 sm:p-8 md:p-12 rounded-lg border border-[#D4C2A4]/20 max-w-md w-full">
              <p className="text-[#D4C2A4] mb-6 font-open-sans text-sm sm:text-base md:text-lg">{error}</p>
              <Button
                onClick={() => window.location.reload()}
                className="bg-[#D4C2A4] text-[#16191D] hover:bg-[#D4C2A4]/90 font-open-sans font-medium px-6 sm:px-8 py-2 sm:py-3 rounded-sm transition-all duration-300 text-sm sm:text-base"
              >
                Try Again
              </Button>
            </div>
          </div>
        </main>
        <Footer isDarkBackground={true} />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#16191D] luxury-scrollbar">
      <Header />
      <main className="">
        {/* Luxury Hero Section */}
        <div className="relative overflow-hidden">
          {/* Background Image with Overlay */}
          <div
            className="absolute inset-0 bg-cover bg-center bg-no-repeat"
            style={{
              backgroundImage: 'url("https://qconvxhtavuzjdwuowpt.supabase.co/storage/v1/object/public/photoz/NEW%20SHIT/image%20(7).png")'
            }}
          />
          <div className="absolute inset-0 bg-gradient-to-br from-[#16191D]/85 via-[#16191D]/70 to-[#16191D]/85" />
          <div className="absolute inset-0 bg-gradient-to-t from-[#16191D] via-transparent to-transparent" />

          {/* Content */}
          <div className="relative z-10 container mx-auto px-4 sm:px-6 py-16 sm:py-20 md:py-32 lg:py-40">
            <div className="max-w-4xl">
              <div className="mb-4 sm:mb-6">
                <span className="inline-block text-[#D4C2A4] font-open-sans text-xs sm:text-sm md:text-base tracking-[0.2em] sm:tracking-[0.3em] uppercase mb-3 sm:mb-4">
                  Premium Safari Collection
                </span>
              </div>
              <h1 className="text-3xl sm:text-4xl md:text-7xl lg:text-8xl font-cormorant font-light text-[#F2EEE6] mb-6 sm:mb-8 leading-[0.9] tracking-wide">
                {tourTypeFilter ? (
                  <>
                    <span className="block">
                      {tourTypeFilter === 'wildlife' && 'Wildlife'}
                      {tourTypeFilter === 'cultural' && 'Cultural'}
                      {tourTypeFilter === 'kilimanjaro' && 'Mount Climbing'}
                    </span>
                    <span className="block text-[#D4C2A4] italic">
                      {tourTypeFilter === 'kilimanjaro' ? 'Adventures' : 'Safari Tours'}
                    </span>
                  </>
                ) : urlFilter ? (
                  <>
                    <span className="block">{urlFilter.charAt(0).toUpperCase() + urlFilter.slice(1)}</span>
                    <span className="block text-[#D4C2A4] italic">Safari Tours</span>
                  </>
                ) : (
                  <>
                    <span className="block">Safari</span>
                    <span className="block text-[#D4C2A4] italic">Tours</span>
                  </>
                )}
              </h1>
              <div className="w-16 sm:w-20 md:w-24 h-px bg-[#D4C2A4] mb-6 sm:mb-8"></div>
              <p className="text-base sm:text-lg md:text-xl font-open-sans text-[#F2EEE6]/80 max-w-2xl leading-relaxed">
                {tourTypeFilter ? (
                  tourTypeFilter === 'wildlife'
                    ? 'Immerse yourself in our exclusive wildlife safari experiences, where you\'ll witness the Big Five and the Great Migration in their natural habitat.'
                    : tourTypeFilter === 'cultural'
                    ? 'Discover authentic cultural experiences with local Maasai communities, traditional ceremonies, and immersive cultural exchanges.'
                    : 'Conquer Africa\'s highest peak with our expertly guided Mount Kilimanjaro climbing adventures, designed for all skill levels.'
                ) : urlFilter
                  ? `Immerse yourself in our exclusive ${urlFilter} safari experiences, where luxury meets the untamed beauty of Tanzania's wilderness.`
                  : 'Discover our meticulously curated collection of safari experiences, where every journey is crafted to exceed your wildest expectations.'
                }
              </p>
            </div>
          </div>
        </div>

        {/* Luxury Controls Section */}
        <div className="bg-gradient-to-b from-[#16191D] to-[#1a1e23] border-t border-[#D4C2A4]/10">
          <div className="container mx-auto px-4 sm:px-6 py-8 sm:py-10 md:py-12">
            {/* Elegant Search and Controls */}
            <div className="max-w-6xl mx-auto">
              <div className="flex flex-col gap-6 sm:gap-8 mb-8 sm:mb-10 md:mb-12">
                {/* Search Section */}
                <div className="w-full">
                  <label className="block text-[#D4C2A4] font-open-sans text-xs sm:text-sm tracking-[0.2em] uppercase mb-3 sm:mb-4">
                    Search Experiences
                  </label>
                  <div className="relative">
                    <Search className="absolute left-4 sm:left-6 top-1/2 h-4 w-4 sm:h-5 sm:w-5 -translate-y-1/2 text-[#D4C2A4]/60" />
                    <Input
                      type="text"
                      placeholder="Discover your perfect safari adventure..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-12 sm:pl-16 pr-4 sm:pr-6 py-3 sm:py-4 bg-[#1a1e23] border-[#D4C2A4]/20 text-[#F2EEE6] placeholder:text-[#F2EEE6]/40 font-open-sans text-sm sm:text-base rounded-sm focus:border-[#D4C2A4] focus:ring-1 focus:ring-[#D4C2A4] transition-all duration-300"
                    />
                  </div>
                </div>

                {/* View Controls */}
                <div className="flex items-center justify-between sm:justify-start sm:gap-6">
                  <span className="text-[#D4C2A4] font-open-sans text-xs sm:text-sm tracking-[0.2em] uppercase">
                    View
                  </span>
                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => setViewMode('grid')}
                      className={`h-10 w-10 sm:h-12 sm:w-12 rounded-sm border transition-all duration-300 ${
                        viewMode === 'grid'
                          ? 'bg-[#D4C2A4] text-[#16191D] border-[#D4C2A4]'
                          : 'bg-transparent text-[#D4C2A4] border-[#D4C2A4]/30 hover:border-[#D4C2A4] hover:bg-[#D4C2A4]/10'
                      }`}
                    >
                      <Grid className="h-4 w-4 sm:h-5 sm:w-5" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => setViewMode('list')}
                      className={`h-10 w-10 sm:h-12 sm:w-12 rounded-sm border transition-all duration-300 ${
                        viewMode === 'list'
                          ? 'bg-[#D4C2A4] text-[#16191D] border-[#D4C2A4]'
                          : 'bg-transparent text-[#D4C2A4] border-[#D4C2A4]/30 hover:border-[#D4C2A4] hover:bg-[#D4C2A4]/10'
                      }`}
                    >
                      <List className="h-4 w-4 sm:h-5 sm:w-5" />
                    </Button>
                  </div>
                </div>
              </div>

              {/* Elegant Results Summary */}
              <div className="flex flex-col gap-4 sm:gap-6 mb-8 sm:mb-10 md:mb-12 pb-6 sm:pb-8 border-b border-[#D4C2A4]/10">
                <div>
                  <p className="text-[#F2EEE6] font-open-sans text-base sm:text-lg mb-2">
                    <span className="text-[#D4C2A4] font-medium">{filteredTours.length}</span> Exceptional Experiences
                    {searchTerm && (
                      <span className="text-[#F2EEE6]/60"> matching "{searchTerm}"</span>
                    )}
                  </p>
                  {(tourTypeFilter || urlFilter) && (
                    <p className="text-[#D4C2A4]/80 font-open-sans text-xs sm:text-sm tracking-wide">
                      {tourTypeFilter ? (
                        tourTypeFilter === 'wildlife' ? 'Wildlife Safari Collection' :
                        tourTypeFilter === 'cultural' ? 'Cultural Safari Collection' :
                        'Mount Climbing Collection'
                      ) : (
                        `${urlFilter.charAt(0).toUpperCase() + urlFilter.slice(1)} Collection`
                      )}
                    </p>
                  )}
                </div>

                {/* Additional Filter Options */}
                <div className="flex items-center gap-3 sm:gap-4 overflow-x-auto">
                  <Sheet open={showFilters} onOpenChange={setShowFilters}>
                    <SheetTrigger asChild>
                      <Button
                        variant="ghost"
                        className="text-[#D4C2A4] border border-[#D4C2A4]/30 hover:border-[#D4C2A4] hover:bg-[#D4C2A4]/10 font-open-sans text-xs sm:text-sm px-4 sm:px-6 py-2 rounded-sm transition-all duration-300 whitespace-nowrap"
                      >
                        <Filter className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                        Filter
                      </Button>
                    </SheetTrigger>
                    <SheetContent side="right" className="w-[400px] sm:w-[540px] bg-[#16191D] border-[#D4C2A4]/20">
                      <SheetHeader>
                        <SheetTitle className="text-[#F2EEE6] font-cormorant text-xl">Filter Tours</SheetTitle>
                        <SheetDescription className="text-[#F2EEE6]/60 font-open-sans">
                          Refine your search to find the perfect safari experience
                        </SheetDescription>
                      </SheetHeader>
                      <div className="mt-6">
                        <TourFilters
                          filters={filters}
                          onFiltersChange={setFilters}
                        />
                      </div>
                    </SheetContent>
                  </Sheet>

                  {/* Clear Filters Button - only show when filters are active */}
                  {(filters.tourType !== 'all' || filters.accommodationLevel !== 'all' ||
                    filters.duration !== 'all' || filters.destinations.length > 0 ||
                    filters.priceRange[0] !== 0 || filters.priceRange[1] !== 10000) && (
                    <Button
                      variant="ghost"
                      onClick={() => {
                        setFilters({
                          tourType: 'all',
                          accommodationLevel: 'all',
                          priceRange: [0, 10000],
                          duration: 'all',
                          destinations: []
                        });
                        // Clear URL parameters as well
                        setSearchParams(prev => {
                          const newParams = new URLSearchParams(prev);
                          newParams.delete('tourType');
                          return newParams;
                        });
                      }}
                      className="text-[#D4C2A4] border border-[#D4C2A4]/30 hover:border-[#D4C2A4] hover:bg-[#D4C2A4]/10 font-open-sans text-xs sm:text-sm px-4 sm:px-6 py-2 rounded-sm transition-all duration-300 whitespace-nowrap"
                    >
                      <X className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                      Clear Filters
                    </Button>
                  )}

                  <Button
                    variant="ghost"
                    className="text-[#D4C2A4] border border-[#D4C2A4]/30 hover:border-[#D4C2A4] hover:bg-[#D4C2A4]/10 font-open-sans text-xs sm:text-sm px-4 sm:px-6 py-2 rounded-sm transition-all duration-300 whitespace-nowrap"
                  >
                    <SortAsc className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                    Sort
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Luxury Tours Collection */}
        <div className="bg-[#1a1e23] py-12 sm:py-14 md:py-16">
          <div className="container mx-auto px-4 sm:px-6">
            <div className="max-w-7xl mx-auto">
              {loading ? (
                <div className="flex items-center justify-center py-16 sm:py-20">
                  <div className="text-center">
                    <Loader2 className="h-6 w-6 sm:h-8 sm:w-8 animate-spin text-[#D4C2A4] mx-auto mb-4" />
                    <span className="text-[#F2EEE6] font-open-sans text-base sm:text-lg">Curating your perfect safari experiences...</span>
                  </div>
                </div>
              ) : (
                <div className={viewMode === 'grid'
                  ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8'
                  : 'space-y-6 sm:space-y-8'}>
                  {filteredTours.map((tour) => (
                    <TourCard
                      key={tour.id}
                      tour={{
                        id: tour.id,
                        title: tour.title,
                        description: tour.description,
                        price: tour.price,
                        duration: tour.duration,
                        image: (tour.images && tour.images[0]) || 'photo-1472396961693-142e6e269027',
                        category: tour.category,
                        accommodationLevel: tour.accommodationLevel,
                        destinations: tour.destinations,
                        rating: tour.rating,
                        reviews: tour.reviewCount
                      }}
                      viewMode={viewMode}
                    />
                  ))}
                </div>
              )}

              {filteredTours.length === 0 && !loading && (
                <div className="text-center py-16 sm:py-20">
                  <div className="max-w-md mx-auto px-4">
                    <div className="w-12 h-12 sm:w-16 sm:h-16 bg-[#D4C2A4]/10 rounded-full flex items-center justify-center mx-auto mb-6">
                      <Search className="h-6 w-6 sm:h-8 sm:w-8 text-[#D4C2A4]" />
                    </div>
                    <h3 className="text-xl sm:text-2xl font-cormorant text-[#F2EEE6] mb-4">
                      No Experiences Found
                    </h3>
                    <p className="text-sm sm:text-base text-[#F2EEE6]/60 font-open-sans leading-relaxed mb-6 sm:mb-8">
                      {tourTypeFilter ? (
                        tourTypeFilter === 'wildlife'
                          ? "We're currently crafting new wildlife safari experiences. Please check back soon or explore our cultural and climbing adventures."
                          : tourTypeFilter === 'cultural'
                          ? "We're currently crafting new cultural safari experiences. Please check back soon or explore our wildlife safaris and climbing adventures."
                          : "We're currently crafting new mount climbing experiences. Please check back soon or explore our wildlife and cultural safaris."
                      ) : urlFilter
                        ? `We're currently crafting new ${urlFilter} safari experiences. Please check back soon or explore our other collections.`
                        : 'We couldn\'t find any safari experiences matching your search. Try adjusting your criteria or explore our featured collections.'
                      }
                    </p>
                    <Button
                      onClick={() => setSearchTerm('')}
                      className="bg-[#D4C2A4] text-[#16191D] hover:bg-[#D4C2A4]/90 font-open-sans font-medium px-6 sm:px-8 py-2 sm:py-3 rounded-sm transition-all duration-300 text-sm sm:text-base"
                    >
                      View All Tours
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
      <Footer isDarkBackground={true} />
    </div>
  );
};

export default Tours;
