/* Luxury Cookie <PERSON>sent Styles */

/* Import luxury fonts for cookie consent */
@import url('https://fonts.googleapis.com/css2?family=Cormorant+Garamond:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap');

/* Enhanced luxury glass container for cookie consent */
.luxury-glass-container {
  background: linear-gradient(135deg, 
    rgba(212, 194, 164, 0.12) 0%,
    rgba(212, 194, 164, 0.08) 50%,
    rgba(212, 194, 164, 0.15) 100%);
  backdrop-filter: blur(25px) saturate(180%);
  -webkit-backdrop-filter: blur(25px) saturate(180%);
  border: 1px solid rgba(212, 194, 164, 0.3);
  box-shadow: 
    0 25px 50px rgba(22, 25, 29, 0.4),
    0 0 0 1px rgba(212, 194, 164, 0.1),
    inset 0 1px 0 rgba(212, 194, 164, 0.2),
    inset 0 -1px 0 rgba(212, 194, 164, 0.1);
  position: relative;
  overflow: hidden;
}

.luxury-glass-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(212, 194, 164, 0.5), 
    transparent);
  animation: luxuryShimmer 3s ease-in-out infinite;
}

.luxury-glass-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 50% 0%, 
    rgba(212, 194, 164, 0.1) 0%, 
    transparent 50%);
  pointer-events: none;
}

/* Premium Button Enhancements */
.luxury-button {
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  border-radius: 12px;
  font-weight: 500;
  letter-spacing: 0.025em;
}

.luxury-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.2), 
    transparent);
  transition: left 0.6s ease;
}

.luxury-button:hover::before {
  left: 100%;
}

.luxury-button:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 
    0 15px 35px rgba(212, 194, 164, 0.3),
    0 0 0 1px rgba(212, 194, 164, 0.2);
}

/* Premium Typography Effects */
.luxury-typography {
  font-family: 'Cormorant Garamond', serif;
  font-feature-settings: "liga" 1, "kern" 1, "swsh" 1, "calt" 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  letter-spacing: 0.025em;
  font-optical-sizing: auto;
}

/* Luxury body text styling */
.luxury-body-text {
  font-family: 'Open Sans', sans-serif;
  font-feature-settings: "liga" 1, "kern" 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  letter-spacing: 0.015em;
  line-height: 1.6;
}

/* Luxury button text styling */
.luxury-button-text {
  font-family: 'Open Sans', sans-serif;
  font-weight: 500;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  font-size: 0.875rem;
}

/* Enhanced heading styles */
.luxury-heading-primary {
  font-family: 'Cormorant Garamond', serif;
  font-weight: 600;
  letter-spacing: 0.02em;
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(22, 25, 29, 0.3);
}

.luxury-heading-secondary {
  font-family: 'Cormorant Garamond', serif;
  font-weight: 500;
  letter-spacing: 0.015em;
  line-height: 1.3;
}

/* Elegant Border Animations */
@keyframes luxuryBorderGlow {
  0%, 100% {
    border-color: rgba(212, 194, 164, 0.4);
    box-shadow: 0 0 10px rgba(212, 194, 164, 0.2);
  }
  50% {
    border-color: rgba(212, 194, 164, 0.8);
    box-shadow: 0 0 25px rgba(212, 194, 164, 0.4);
  }
}

.luxury-border-glow {
  animation: luxuryBorderGlow 3s ease-in-out infinite;
}

/* Premium Shimmer Animation */
@keyframes luxuryShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Cookie Dialog Specific Enhancements */
.cookie-dialog-content {
  background: linear-gradient(135deg, 
    rgba(22, 25, 29, 0.95) 0%,
    rgba(26, 30, 35, 0.95) 50%,
    rgba(22, 25, 29, 0.95) 100%);
}

/* Custom Switch Styling for Cookie Preferences */
.cookie-switch[data-state="checked"] {
  background: linear-gradient(135deg, #D4C2A4 0%, #C4B294 100%);
  border-color: #D4C2A4;
}

.cookie-switch[data-state="unchecked"] {
  background: rgba(212, 194, 164, 0.2);
  border-color: rgba(212, 194, 164, 0.4);
}

.cookie-switch:focus-visible {
  outline: 2px solid #D4C2A4;
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(212, 194, 164, 0.2);
}

/* Cookie Card Hover Effects */
.cookie-card {
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}

.cookie-card:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 20px 40px rgba(22, 25, 29, 0.3),
    0 0 0 1px rgba(212, 194, 164, 0.2),
    inset 0 1px 0 rgba(212, 194, 164, 0.1);
  border-color: rgba(212, 194, 164, 0.4);
}

/* Floating Elements Animation */
@keyframes luxuryFloat {
  0%, 100% {
    transform: translateY(0px) scale(1);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-10px) scale(1.1);
    opacity: 0.6;
  }
}

.luxury-particle {
  animation: luxuryFloat 6s ease-in-out infinite;
}

.luxury-particle:nth-child(2) {
  animation-delay: 2s;
}

.luxury-particle:nth-child(3) {
  animation-delay: 4s;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
  .luxury-glass-container {
    backdrop-filter: blur(20px) saturate(150%);
    -webkit-backdrop-filter: blur(20px) saturate(150%);
    margin: 0.5rem;
    border-radius: 16px;
  }
  
  .luxury-button {
    padding: 12px 20px;
    font-size: 14px;
  }
  
  .luxury-button:hover {
    transform: translateY(-1px) scale(1.01);
  }
}

@media (max-width: 480px) {
  .luxury-glass-container {
    backdrop-filter: blur(15px) saturate(120%);
    -webkit-backdrop-filter: blur(15px) saturate(120%);
    margin: 0.25rem;
    border-radius: 12px;
  }
  
  .luxury-button {
    padding: 10px 16px;
    font-size: 13px;
  }
  
  .cookie-card {
    margin-bottom: 1rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .luxury-glass-container {
    background: rgba(212, 194, 164, 0.2);
    border-color: rgba(212, 194, 164, 0.6);
  }
  
  .luxury-button {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .luxury-particle,
  .luxury-border-glow,
  .luxury-glass-container::before,
  .luxury-glass-container::after {
    animation: none;
  }
  
  .luxury-button::before {
    transition: none;
  }
  
  .luxury-button:hover,
  .cookie-card:hover {
    transform: none;
  }
  
  * {
    transition-duration: 0.01ms !important;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .luxury-button:hover {
    transform: scale(0.98);
  }
  
  .luxury-glass-container {
    -webkit-tap-highlight-color: rgba(212, 194, 164, 0.1);
  }
  
  .cookie-card:hover {
    transform: none;
  }
}

/* Safari-specific enhancements */
@supports (-webkit-backdrop-filter: blur(25px)) {
  .luxury-glass-container {
    -webkit-backdrop-filter: blur(25px) saturate(180%);
  }
}

/* Enhanced focus states for accessibility */
.luxury-button:focus-visible {
  outline: 2px solid #D4C2A4;
  outline-offset: 2px;
  box-shadow:
    0 0 0 4px rgba(212, 194, 164, 0.2),
    0 15px 30px rgba(212, 194, 164, 0.4);
}

/* Ensure button text color remains black on gradient buttons */
.luxury-button[class*="text-[#16191D]"] {
  color: #16191D !important;
}

.luxury-button[class*="text-[#16191D]"]:hover {
  color: #16191D !important;
}

/* Cookie Banner Slide-up Animation */
@keyframes slideUpLuxury {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.cookie-banner-enter {
  animation: slideUpLuxury 0.6s cubic-bezier(0.23, 1, 0.32, 1);
}

/* Dialog Fade-in Animation */
@keyframes fadeInLuxury {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.cookie-dialog-enter {
  animation: fadeInLuxury 0.4s cubic-bezier(0.23, 1, 0.32, 1);
}

/* Luxury text glow effects */
.luxury-text-glow {
  text-shadow:
    0 0 10px rgba(212, 194, 164, 0.3),
    0 0 20px rgba(212, 194, 164, 0.2),
    0 0 30px rgba(212, 194, 164, 0.1);
}

/* Premium icon styling */
.luxury-icon {
  filter: drop-shadow(0 2px 4px rgba(212, 194, 164, 0.3));
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}

.luxury-icon:hover {
  filter: drop-shadow(0 4px 8px rgba(212, 194, 164, 0.4));
  transform: scale(1.05);
}

/* Enhanced scrollbar for cookie dialog */
.cookie-dialog-content::-webkit-scrollbar {
  width: 8px;
}

.cookie-dialog-content::-webkit-scrollbar-track {
  background: rgba(212, 194, 164, 0.1);
  border-radius: 4px;
}

.cookie-dialog-content::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #D4C2A4, #C4B294);
  border-radius: 4px;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.cookie-dialog-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #C4B294, #D4C2A4);
}

/* Luxury focus ring */
.luxury-focus-ring:focus-visible {
  outline: none;
  box-shadow:
    0 0 0 2px rgba(212, 194, 164, 0.4),
    0 0 0 4px rgba(212, 194, 164, 0.2),
    0 8px 16px rgba(212, 194, 164, 0.3);
}

/* Premium card elevation */
.luxury-card-elevation {
  box-shadow:
    0 4px 6px rgba(22, 25, 29, 0.1),
    0 1px 3px rgba(22, 25, 29, 0.08),
    inset 0 1px 0 rgba(212, 194, 164, 0.1);
}

.luxury-card-elevation:hover {
  box-shadow:
    0 10px 25px rgba(22, 25, 29, 0.15),
    0 4px 10px rgba(22, 25, 29, 0.1),
    inset 0 1px 0 rgba(212, 194, 164, 0.2);
}
