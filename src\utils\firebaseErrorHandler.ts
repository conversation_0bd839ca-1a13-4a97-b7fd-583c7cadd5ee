/**
 * Firebase Error Handler for Warriors Safari
 * Masks all Firebase errors with custom, user-friendly messages
 */

import { trackError } from './analytics';

// Firebase error codes and their custom Warriors Safari messages
const FIREBASE_ERROR_MESSAGES: Record<string, string> = {
  // Authentication Errors
  'auth/user-not-found': 'We couldn\'t find an account with that email. Please check your email or create a new account to start your safari adventure.',
  'auth/wrong-password': 'The password you entered is incorrect. Please try again or reset your password to continue your safari journey.',
  'auth/invalid-email': 'Please enter a valid email address to access your safari account.',
  'auth/email-already-in-use': 'An account with this email already exists. Please sign in or use a different email to join our safari community.',
  'auth/weak-password': 'Please choose a stronger password (at least 6 characters) to secure your safari account.',
  'auth/too-many-requests': 'Too many login attempts. Please wait a moment before trying again, or contact our safari support team.',
  'auth/user-disabled': 'This account has been temporarily disabled. Please contact our safari support team for assistance.',
  'auth/operation-not-allowed': 'This sign-in method is not currently available. Please try a different method or contact our safari support team.',
  'auth/invalid-credential': 'The credentials you provided are invalid. Please check your information and try again.',
  'auth/credential-already-in-use': 'These credentials are already associated with another account. Please use different credentials.',
  'auth/requires-recent-login': 'For your security, please sign in again to complete this action.',
  'auth/network-request-failed': 'Network connection issue. Please check your internet connection and try again.',

  // Firestore Errors
  'firestore/permission-denied': 'You don\'t have permission to access this safari information. Please sign in or contact our support team.',
  'firestore/not-found': 'The safari information you\'re looking for could not be found. It may have been moved or removed.',
  'firestore/already-exists': 'This safari booking or information already exists in our system.',
  'firestore/resource-exhausted': 'Our safari booking system is experiencing high demand. Please try again in a few moments.',
  'firestore/failed-precondition': 'Unable to complete your safari request due to current system conditions. Please try again.',
  'firestore/aborted': 'Your safari request was interrupted. Please try again.',
  'firestore/out-of-range': 'The safari information you requested is outside the available range.',
  'firestore/unimplemented': 'This safari feature is not yet available. We\'re working to bring it to you soon!',
  'firestore/internal': 'Our safari booking system encountered an internal issue. Our team has been notified.',
  'firestore/unavailable': 'Our safari booking system is temporarily unavailable. Please try again in a few moments.',
  'firestore/data-loss': 'There was an issue with your safari data. Please contact our support team immediately.',
  'firestore/unauthenticated': 'Please sign in to access your safari account and bookings.',
  'firestore/invalid-argument': 'The safari information provided is invalid. Please check your details and try again.',
  'firestore/deadline-exceeded': 'Your safari request is taking longer than expected. Please try again.',
  'firestore/cancelled': 'Your safari request was cancelled. Please try again if needed.',

  // Storage Errors
  'storage/object-not-found': 'The safari image or file you\'re looking for could not be found.',
  'storage/bucket-not-found': 'Safari media storage is temporarily unavailable. Please try again later.',
  'storage/project-not-found': 'Safari media service configuration issue. Our team has been notified.',
  'storage/quota-exceeded': 'Safari media storage limit reached. Please contact our support team.',
  'storage/unauthenticated': 'Please sign in to upload or access safari media.',
  'storage/unauthorized': 'You don\'t have permission to access this safari media.',
  'storage/retry-limit-exceeded': 'Safari media upload failed after multiple attempts. Please try again later.',
  'storage/invalid-checksum': 'Safari media upload verification failed. Please try uploading again.',
  'storage/canceled': 'Safari media upload was cancelled. Please try again if needed.',
  'storage/invalid-event-name': 'Safari media operation failed due to invalid request.',
  'storage/invalid-url': 'Invalid safari media URL. Please check the link and try again.',
  'storage/invalid-argument': 'Invalid safari media upload parameters. Please check your file and try again.',
  'storage/no-default-bucket': 'Safari media storage is not properly configured. Our team has been notified.',
  'storage/cannot-slice-blob': 'Safari media file processing failed. Please try a different file.',
  'storage/server-file-wrong-size': 'Safari media file size mismatch. Please try uploading again.',

  // Functions Errors
  'functions/cancelled': 'Your safari request was cancelled. Please try again if needed.',
  'functions/unknown': 'Our safari system encountered an unexpected issue. Our team has been notified.',
  'functions/invalid-argument': 'Invalid safari request parameters. Please check your information and try again.',
  'functions/deadline-exceeded': 'Your safari request is taking longer than expected. Please try again.',
  'functions/not-found': 'The safari service you requested is not available.',
  'functions/already-exists': 'This safari request already exists in our system.',
  'functions/permission-denied': 'You don\'t have permission to perform this safari action.',
  'functions/resource-exhausted': 'Our safari system is experiencing high demand. Please try again in a few moments.',
  'functions/failed-precondition': 'Unable to complete your safari request due to current conditions.',
  'functions/aborted': 'Your safari request was interrupted. Please try again.',
  'functions/out-of-range': 'Your safari request parameters are outside the acceptable range.',
  'functions/unimplemented': 'This safari feature is not yet available. We\'re working to bring it to you soon!',
  'functions/internal': 'Our safari system encountered an internal issue. Our team has been notified.',
  'functions/unavailable': 'Safari services are temporarily unavailable. Please try again in a few moments.',
  'functions/data-loss': 'There was an issue with your safari data. Please contact our support team immediately.',
  'functions/unauthenticated': 'Please sign in to access safari services.',

  // Remote Config Errors
  'remoteconfig/fetch-throttled': 'Safari configuration updates are temporarily limited. Please try again later.',
  'remoteconfig/fetch-timeout': 'Safari configuration update timed out. Please try again.',

  // App Check Errors
  'appcheck/fetch-status-unknown': 'Safari app verification status unknown. Please try again.',
  'appcheck/fetch-status-success': 'Safari app verification successful.',
  'appcheck/fetch-status-unauthenticated': 'Safari app verification failed. Please try again.',

  // Network and General Errors
  'network-request-failed': 'Network connection issue. Please check your internet connection and try again.',
  'timeout': 'Your safari request timed out. Please try again.',
  'unavailable': 'Safari services are temporarily unavailable. Please try again in a few moments.',
  'internal': 'Our safari system encountered an internal issue. Our team has been notified.',
};

// Default messages for different error categories
const DEFAULT_ERROR_MESSAGES = {
  auth: 'We\'re having trouble with your safari account access. Please try again or contact our support team.',
  firestore: 'We\'re experiencing issues with our safari booking system. Please try again in a few moments.',
  storage: 'We\'re having trouble with safari media uploads. Please try again later.',
  functions: 'Our safari services are temporarily experiencing issues. Please try again shortly.',
  network: 'Please check your internet connection and try again.',
  general: 'We\'re experiencing technical difficulties with our safari system. Our team has been notified and is working to resolve this quickly.'
};

// Contact information for support
const SUPPORT_CONTACT = {
  email: '<EMAIL>',
  phone: '+255 123 456 789',
  whatsapp: '+255 123 456 789'
};

export interface CustomFirebaseError {
  originalError: any;
  customMessage: string;
  errorCode: string;
  category: string;
  supportInfo?: typeof SUPPORT_CONTACT;
  timestamp: Date;
}

/**
 * Main function to handle and mask Firebase errors
 */
export function handleFirebaseError(error: any, context?: string): CustomFirebaseError {
  const timestamp = new Date();
  let errorCode = 'unknown';
  let category = 'general';
  let customMessage = DEFAULT_ERROR_MESSAGES.general;

  // Extract error code and determine category
  if (error?.code) {
    errorCode = error.code;
    
    if (errorCode.startsWith('auth/')) {
      category = 'auth';
      customMessage = FIREBASE_ERROR_MESSAGES[errorCode] || DEFAULT_ERROR_MESSAGES.auth;
    } else if (errorCode.startsWith('firestore/')) {
      category = 'firestore';
      customMessage = FIREBASE_ERROR_MESSAGES[errorCode] || DEFAULT_ERROR_MESSAGES.firestore;
    } else if (errorCode.startsWith('storage/')) {
      category = 'storage';
      customMessage = FIREBASE_ERROR_MESSAGES[errorCode] || DEFAULT_ERROR_MESSAGES.storage;
    } else if (errorCode.startsWith('functions/')) {
      category = 'functions';
      customMessage = FIREBASE_ERROR_MESSAGES[errorCode] || DEFAULT_ERROR_MESSAGES.functions;
    } else if (errorCode.includes('network') || errorCode.includes('timeout')) {
      category = 'network';
      customMessage = FIREBASE_ERROR_MESSAGES[errorCode] || DEFAULT_ERROR_MESSAGES.network;
    } else {
      customMessage = FIREBASE_ERROR_MESSAGES[errorCode] || DEFAULT_ERROR_MESSAGES.general;
    }
  } else if (error?.message) {
    // Handle errors without codes but with messages
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch') || message.includes('connection')) {
      category = 'network';
      customMessage = DEFAULT_ERROR_MESSAGES.network;
    } else if (message.includes('permission') || message.includes('unauthorized')) {
      category = 'auth';
      customMessage = 'You don\'t have permission to perform this action. Please sign in or contact our safari support team.';
    } else if (message.includes('not found')) {
      category = 'firestore';
      customMessage = 'The safari information you\'re looking for could not be found.';
    }
  }

  // Track the error for analytics (with original error for internal tracking)
  trackError(
    `${category}: ${customMessage}`,
    errorCode,
    context || window.location.pathname
  );

  // Log original error for debugging (only in development)
  if (process.env.NODE_ENV === 'development') {
    console.group('🔥 Firebase Error Handled');
    console.error('Original Error:', error);
    console.log('Error Code:', errorCode);
    console.log('Category:', category);
    console.log('Custom Message:', customMessage);
    console.log('Context:', context);
    console.groupEnd();
  }

  const customError: CustomFirebaseError = {
    originalError: error,
    customMessage,
    errorCode,
    category,
    supportInfo: category === 'general' ? SUPPORT_CONTACT : undefined,
    timestamp
  };

  return customError;
}

/**
 * Wrapper function for Firebase operations with automatic error handling
 */
export async function withFirebaseErrorHandling<T>(
  operation: () => Promise<T>,
  context?: string
): Promise<T> {
  try {
    return await operation();
  } catch (error) {
    const customError = handleFirebaseError(error, context);
    
    // Create a new error with the custom message but preserve the original error
    const maskedError = new Error(customError.customMessage);
    (maskedError as any).originalError = error;
    (maskedError as any).errorCode = customError.errorCode;
    (maskedError as any).category = customError.category;
    (maskedError as any).supportInfo = customError.supportInfo;
    
    throw maskedError;
  }
}

/**
 * Get user-friendly error message for display in UI
 */
export function getFirebaseErrorMessage(error: any): string {
  if (error?.customMessage) {
    return error.customMessage;
  }
  
  const customError = handleFirebaseError(error);
  return customError.customMessage;
}

/**
 * Check if an error is a Firebase error
 */
export function isFirebaseError(error: any): boolean {
  return error?.code && (
    error.code.startsWith('auth/') ||
    error.code.startsWith('firestore/') ||
    error.code.startsWith('storage/') ||
    error.code.startsWith('functions/') ||
    error.code.startsWith('remoteconfig/') ||
    error.code.startsWith('appcheck/')
  );
}

/**
 * Get support contact information based on error category
 */
export function getSupportInfo(error: any): typeof SUPPORT_CONTACT | null {
  const customError = handleFirebaseError(error);
  return customError.supportInfo || null;
}

/**
 * Format error for user display with optional support information
 */
export function formatErrorForUser(error: any, includeSupport: boolean = false): string {
  const customError = handleFirebaseError(error);
  let message = customError.customMessage;
  
  if (includeSupport && customError.supportInfo) {
    message += `\n\nNeed help? Contact our safari support team:\n📧 ${customError.supportInfo.email}\n📞 ${customError.supportInfo.phone}`;
  }
  
  return message;
}
