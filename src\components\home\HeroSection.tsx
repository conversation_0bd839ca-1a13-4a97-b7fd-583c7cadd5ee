import React, { useState, useEffect, useRef, memo } from 'react';
import { Link } from 'react-router-dom';
import { Volume2, VolumeX } from 'lucide-react'; // Changed icons for sound control

const HeroSection = memo(() => {
  const [isMobile, setIsMobile] = useState(false);
  const [isMuted, setIsMuted] = useState(true); // State to track mute status
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // Set up the video player
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    // Autoplay is handled by the video tag attributes
    video.play().catch(error => {
      console.error("Autoplay was prevented: ", error);
    });

    const handleVolumeChange = () => {
      if (video) {
        setIsMuted(video.muted);
      }
    };

    video.addEventListener('volumechange', handleVolumeChange);

    return () => {
      video.removeEventListener('volumechange', handleVolumeChange);
    };
  }, []);


  const toggleMute = () => {
    const video = videoRef.current;
    if (!video) return;

    video.muted = !video.muted;
  };

  const currentVideoSrc = "https://firebasestorage.googleapis.com/v0/b/warriorsofafricasafari-5bb0d.firebasestorage.app/o/imagez%2Fwarriorsvideo.webm?alt=media&token=87fb6618-c3e9-4960-9b23-3b6a00f3f807";

  return (
    <section className="hero-section">
      <video
        ref={videoRef}
        className="hero-video"
        autoPlay
        muted // Start muted
        loop
        playsInline
        preload="metadata"
        poster=""
        src={currentVideoSrc}
        style={{
          opacity: 1,
          transition: 'opacity 0.5s ease-in-out'
        }}
      >
        Your browser does not support the video tag.
      </video>

      <div className="hero-overlay"></div>

      <div className="hero-content">
        <div className="flex flex-col items-center mb-8">
          <img
             src="/photos/fulllogo.svg"
             alt="Warriors of Africa Safari Logo"
             className="mb-4 w-64 h-auto md:w-96"
          />
        </div>

        <h1 className="hero-headline">
          Welcome to the<br />
          Land of <em>Endless</em><br />
          Safari
        </h1>

        <Link to="/tour-builder" className="hero-cta-button">
          JOIN THE SAFARI
        </Link>
      </div>

      <button
        onClick={toggleMute}
        className="hero-play-pause-button" // You might want to rename this CSS class
        aria-label={isMuted ? 'Unmute video' : 'Mute video'}
      >
        {isMuted ? <VolumeX className="w-6 h-6" /> : <Volume2 className="w-6 h-6" />}
      </button>
    </section>
  );
});

HeroSection.displayName = 'HeroSection';

export default HeroSection;