// Tour Management Test Utility
// This file provides functions to test the tour management fix

import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';

// Test function to verify if a tour document exists
export async function testTourExists(tourId: string): Promise<{ exists: boolean; data?: any; error?: string }> {
  try {
    const docRef = doc(db, 'tours', tourId);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return {
        exists: true,
        data: { id: docSnap.id, ...docSnap.data() }
      };
    } else {
      return {
        exists: false
      };
    }
  } catch (error) {
    return {
      exists: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Test function to validate tour data structure
export function validateTourStructure(tourData: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  const requiredFields = ['title', 'description', 'price', 'duration', 'location', 'tourType'];
  
  // Check required fields
  requiredFields.forEach(field => {
    if (!tourData[field]) {
      errors.push(`Missing required field: ${field}`);
    }
  });
  
  // Check data types
  if (tourData.price && (typeof tourData.price !== 'number' || tourData.price <= 0)) {
    errors.push('Price must be a positive number');
  }
  
  if (tourData.maxGroupSize && (typeof tourData.maxGroupSize !== 'number' || tourData.maxGroupSize <= 0)) {
    errors.push('Max group size must be a positive number');
  }
  
  if (tourData.minGroupSize && (typeof tourData.minGroupSize !== 'number' || tourData.minGroupSize <= 0)) {
    errors.push('Min group size must be a positive number');
  }
  
  // Check tour type validity
  const validTourTypes = ['kilimanjaro', 'wildlife', 'cultural'];
  if (tourData.tourType && !validTourTypes.includes(tourData.tourType)) {
    errors.push(`Invalid tour type: ${tourData.tourType}. Valid types: ${validTourTypes.join(', ')}`);
  }
  
  // Check array fields
  const arrayFields = ['destinations', 'activities', 'accommodations', 'includes', 'excludes', 'images'];
  arrayFields.forEach(field => {
    if (tourData[field] && !Array.isArray(tourData[field])) {
      errors.push(`${field} must be an array`);
    }
  });
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// Test function to simulate the fixed handleSave logic
export async function testSaveLogic(tourData: any, editingTourId?: string): Promise<{ success: boolean; action: string; error?: string }> {
  try {
    // Validate data first
    const validation = validateTourStructure(tourData);
    if (!validation.isValid) {
      return {
        success: false,
        action: 'validation',
        error: validation.errors.join(', ')
      };
    }
    
    if (editingTourId) {
      // Test update logic
      const existsResult = await testTourExists(editingTourId);
      
      if (existsResult.error) {
        return {
          success: false,
          action: 'check_existence',
          error: existsResult.error
        };
      }
      
      if (existsResult.exists) {
        return {
          success: true,
          action: 'update',
        };
      } else {
        return {
          success: true,
          action: 'create_fallback',
        };
      }
    } else {
      // Test create logic
      return {
        success: true,
        action: 'create',
      };
    }
  } catch (error) {
    return {
      success: false,
      action: 'error',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Test function to run comprehensive tests
export async function runTourManagementTests(): Promise<{ passed: number; failed: number; results: string[] }> {
  const results: string[] = [];
  let passed = 0;
  let failed = 0;
  
  // Test 1: Valid tour data validation
  const validTour = {
    title: 'Test Safari',
    description: 'A test safari tour',
    price: 2500,
    duration: '7 days',
    location: 'Tanzania',
    tourType: 'wildlife',
    destinations: ['Serengeti'],
    activities: ['Game Drive'],
    accommodations: ['Lodge'],
    includes: ['Meals'],
    excludes: ['Flights'],
    images: ['test.jpg']
  };
  
  const validationResult = validateTourStructure(validTour);
  if (validationResult.isValid) {
    results.push('✅ Valid tour data validation passed');
    passed++;
  } else {
    results.push(`❌ Valid tour data validation failed: ${validationResult.errors.join(', ')}`);
    failed++;
  }
  
  // Test 2: Invalid tour data validation
  const invalidTour = {
    title: '',
    price: -100,
    tourType: 'invalid'
  };
  
  const invalidValidationResult = validateTourStructure(invalidTour);
  if (!invalidValidationResult.isValid) {
    results.push('✅ Invalid tour data validation passed (correctly rejected)');
    passed++;
  } else {
    results.push('❌ Invalid tour data validation failed (incorrectly accepted)');
    failed++;
  }
  
  // Test 3: Save logic for new tour
  const newTourSaveResult = await testSaveLogic(validTour);
  if (newTourSaveResult.success && newTourSaveResult.action === 'create') {
    results.push('✅ New tour save logic passed');
    passed++;
  } else {
    results.push(`❌ New tour save logic failed: ${newTourSaveResult.error || 'Unknown error'}`);
    failed++;
  }
  
  // Test 4: Save logic for non-existent tour update
  const nonExistentTourSaveResult = await testSaveLogic(validTour, 'non-existent-tour-id');
  if (nonExistentTourSaveResult.success && nonExistentTourSaveResult.action === 'create_fallback') {
    results.push('✅ Non-existent tour update logic passed (fallback to create)');
    passed++;
  } else {
    results.push(`❌ Non-existent tour update logic failed: ${nonExistentTourSaveResult.error || 'Unknown error'}`);
    failed++;
  }
  
  return { passed, failed, results };
}

// Export for console testing
export function logTourManagementTestResults(): void {
  console.log('🔧 Running Tour Management Tests...\n');
  runTourManagementTests().then(({ passed, failed, results }) => {
    results.forEach(result => console.log(result));
    
    console.log(`\n📊 Test Results: ${passed} passed, ${failed} failed`);
    
    if (failed === 0) {
      console.log('🎉 All tour management tests passed!');
    } else {
      console.log('⚠️ Some tests failed. Please review the tour management system.');
    }
  });
}
