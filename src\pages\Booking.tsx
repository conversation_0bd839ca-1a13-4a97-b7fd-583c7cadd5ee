
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import PageLoader from '@/components/ui/PageLoader';
import EnhancedBookingForm from '@/components/booking/EnhancedBookingForm';
import { Clock, Users, Star, Shield, Award, CheckCircle2 } from 'lucide-react';
import { FirebaseService } from '@/services/firebase';
import { Tour } from '@/types/firebase';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';

interface BookingData {
  tourId: string;
  startDate: Date | null;
  groupSize: number;
  childrenCount: number;
  travelers: Array<{
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    dateOfBirth: Date | null;
    passportNumber: string;
    nationality: string;
  }>;
  accommodation: string;
  addOns: string[];
  specialRequests: string;
  totalPrice: number;
  dietaryRestrictions: string[];
  emergencyContact: {
    name: string;
    phone: string;
    relationship: string;
  };
}

const Booking = () => {
  const { tourId } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { currentUser } = useAuth();
  
  const [tour, setTour] = useState<Tour | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentStep, setCurrentStep] = useState(1);
  
  const [bookingData, setBookingData] = useState<BookingData>({
    tourId: tourId || '',
    startDate: null,
    groupSize: 1,
    childrenCount: 0,
    travelers: [{
      firstName: '',
      lastName: '',
      email: currentUser?.email || '',
      phone: '',
      dateOfBirth: null,
      passportNumber: '',
      nationality: '',
    }],
    accommodation: 'midrange',
    addOns: [],
    specialRequests: '',
    totalPrice: 0,
    dietaryRestrictions: [],
    emergencyContact: {
      name: '',
      phone: '',
      relationship: ''
    }
  });

  useEffect(() => {
    const loadTour = async () => {
      if (!tourId) {
        navigate('/tours');
        return;
      }

      try {
        setLoading(true);
        console.log('Loading tour with ID:', tourId); // Debug log
        const tourData = await FirebaseService.getTour(tourId);
        console.log('Tour data received:', tourData); // Debug log

        if (tourData) {
          setTour(tourData as Tour);
          setBookingData(prev => ({
            ...prev,
             totalPrice: (tourData as Tour).price || 0
          }));
        } else {
          console.error('Tour not found for ID:', tourId); // Debug log
          toast({
            title: "Tour not found",
            description: "The requested tour could not be found. Please check the tour ID and try again.",
            variant: "destructive"
          });
          navigate('/tours');
        }
      } catch (error) {
        console.error('Error loading tour:', error);
        toast({
          title: "Error loading tour",
          description: "There was an error loading the tour details. Please try again later.",
          variant: "destructive"
        });
        navigate('/tours');
      } finally {
        setLoading(false);
      }
    };

    loadTour();
  }, [tourId, navigate, toast]);

  // Check if user is authenticated
  useEffect(() => {
    if (!currentUser) {
      toast({
        title: "Authentication Required",
        description: "Please sign in to make a booking.",
        variant: "destructive"
      });
      navigate('/login');
    }
  }, [currentUser, navigate, toast]);

  const handleDataChange = (field: keyof BookingData, value: any) => {
    setBookingData(prev => {
      const updated = { ...prev, [field]: value };
      
      // Recalculate total price when relevant fields change
      if (field === 'groupSize' || field === 'childrenCount' || field === 'accommodation' || field === 'addOns') {
        const basePrice = tour?.price || 0;
        const accommodationPrices = {
          budget: 0,
          standard: 200,
          luxury: 500
        };
        const addOnPrices = {
          photography: 75,
          cultural: 50,
          balloon: 550,
          'night-drive': 100
        };

        const accommodationCost = accommodationPrices[updated.accommodation as keyof typeof accommodationPrices] || 0;
        const addOnsCost = updated.addOns.reduce((sum, addon) =>
          sum + (addOnPrices[addon as keyof typeof addOnPrices] || 0), 0
        );

        // Children get 50% discount on base price and accommodation
        const adultPrice = (basePrice + accommodationCost) * updated.groupSize;
        const childrenPrice = (basePrice + accommodationCost) * updated.childrenCount * 0.5;

        updated.totalPrice = adultPrice + childrenPrice + addOnsCost;
      }
      
      return updated;
    });
  };

  const handleNext = () => {
    setCurrentStep(prev => Math.min(prev + 1, 4));
  };

  const handlePrev = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleBookingComplete = () => {
    toast({
      title: "Booking Confirmed!",
      description: "Your booking has been successfully submitted. You will receive a confirmation email shortly."
    });
    navigate('/user-dashboard');
  };

  if (loading) {
    return (
      <PageLoader
        title="Loading Booking..."
        subtitle="Preparing your safari booking experience..."
      />
    );
  }

  if (!tour || !currentUser) {
    return null;
  }

  return (
    <div className="min-h-screen relative">
      {/* Luxury Background - Only for main content area */}
      <div className="absolute inset-0 bg-[#16191D]" style={{ bottom: '200px' }}></div>
      <div className="absolute inset-0 bg-gradient-to-br from-[#16191D] via-[#1a1e23] to-[#16191D]" style={{ bottom: '200px' }}></div>
      <div className="absolute inset-0 bg-gradient-to-br from-[#D4C2A4]/5 to-transparent" style={{ bottom: '200px' }}></div>

      {/* Subtle Pattern Overlay */}
      <div className="absolute inset-0 opacity-5" style={{ bottom: '200px' }}>
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4C2A4' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          backgroundRepeat: 'repeat'
        }}></div>
      </div>

      <Header />
      <main className="relative pt-20 sm:pt-24 pb-16 sm:pb-20 bg-[#16191D]">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="max-w-7xl mx-auto">
            {/* Luxury Header - Mobile Optimized */}
            <div className="text-center mb-8 sm:mb-12">
              <div className="inline-flex items-center gap-2 sm:gap-3 mb-4 sm:mb-6">
                <div className="w-8 sm:w-12 h-[1px] bg-gradient-to-r from-transparent to-[#D4C2A4]"></div>
                <Award className="w-5 h-5 sm:w-6 sm:h-6 text-[#D4C2A4]" />
                <div className="w-8 sm:w-12 h-[1px] bg-gradient-to-l from-transparent to-[#D4C2A4]"></div>
              </div>
              <h1 className="font-cormorant text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-semibold text-[#F2EEE6] mb-4 sm:mb-6 tracking-wide px-2">
                Complete Your Booking
              </h1>
              <p className="font-open-sans text-base sm:text-lg md:text-xl text-[#A9A9A9] max-w-2xl mx-auto leading-relaxed px-4">
                Secure your exclusive safari adventure with our premium booking experience
              </p>
            </div>

            {/* Luxury Progress Bar - Mobile Optimized */}
            <div className="mb-8 sm:mb-12">
              <div className="relative">
                {/* Glass morphism container */}
                <div className="backdrop-blur-xl bg-white/5 border border-[#D4C2A4]/20 rounded-xl sm:rounded-2xl p-4 sm:p-6 md:p-8 shadow-2xl">
                  <div className="relative">
                    {/* Background connector line */}
                    <div className="absolute top-5 sm:top-6 md:top-7 left-0 right-0 h-[2px] bg-white/10 rounded-full"></div>

                    {/* Progress connector line */}
                    <div
                      className="absolute top-5 sm:top-6 md:top-7 left-0 h-[2px] bg-gradient-to-r from-[#D4C2A4] to-[#B8A082] rounded-full transition-all duration-700 ease-out"
                      style={{
                        width: `${((currentStep - 1) / 3) * 100}%`
                      }}
                    ></div>

                    {/* Steps */}
                    <div className="grid grid-cols-4 gap-2 sm:gap-4">
                      {[
                        { number: 1, label: 'Dates & Group' },
                        { number: 2, label: 'Traveler Info' },
                        { number: 3, label: 'Customize' },
                        { number: 4, label: 'Payment' }
                      ].map((step) => (
                        <div key={step.number} className="flex flex-col items-center">
                          {/* Step Circle */}
                          <div className="relative z-10 mb-3 sm:mb-4">
                            <div className={`w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 rounded-full flex items-center justify-center text-xs sm:text-sm md:text-base font-semibold transition-all duration-500 ${
                              currentStep >= step.number
                                ? 'bg-gradient-to-br from-[#D4C2A4] to-[#B8A082] text-[#16191D] shadow-lg shadow-[#D4C2A4]/25'
                                : 'bg-white/10 text-[#A9A9A9] border border-[#A9A9A9]/30'
                            }`}>
                              {currentStep > step.number ? (
                                <CheckCircle2 className="w-4 h-4 sm:w-5 sm:h-5" />
                              ) : (
                                step.number
                              )}
                            </div>
                            {currentStep >= step.number && (
                              <div className="absolute inset-0 rounded-full bg-[#D4C2A4]/20 animate-pulse"></div>
                            )}
                          </div>

                          {/* Step Label */}
                          <div className="text-center px-1">
                            <span className={`font-open-sans text-xs sm:text-sm md:text-base transition-colors duration-300 leading-tight ${
                              currentStep >= step.number ? 'text-[#D4C2A4]' : 'text-[#A9A9A9]'
                            }`}>
                              {step.label}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-12">
              {/* Main Form */}
              <div className="lg:col-span-2 order-2 lg:order-1">
                <EnhancedBookingForm
                  tourId={tour.id}
                  currentStep={currentStep}
                  bookingData={bookingData}
                  onDataChange={handleDataChange}
                  onNext={handleNext}
                  onPrev={handlePrev}
                  customTour={tour}
                />
              </div>

              {/* Luxury Booking Summary Sidebar - Mobile Optimized */}
              <div className="lg:col-span-1 order-1 lg:order-2">
                <div className="lg:sticky lg:top-32">
                  {/* Glass morphism card - Mobile Optimized */}
                  <div className="backdrop-blur-xl bg-white/5 border border-[#D4C2A4]/20 rounded-xl sm:rounded-2xl shadow-2xl overflow-hidden">
                    {/* Header with gradient */}
                    <div className="bg-gradient-to-r from-[#D4C2A4]/10 to-[#B8A082]/10 border-b border-[#D4C2A4]/20 p-4 sm:p-6">
                      <div className="flex items-center gap-2 sm:gap-3">
                        <Shield className="w-5 h-5 sm:w-6 sm:h-6 text-[#D4C2A4]" />
                        <h3 className="font-cormorant text-lg sm:text-xl font-semibold text-[#F2EEE6]">
                          Booking Summary
                        </h3>
                      </div>
                    </div>
                    {/* Content */}
                    <div className="p-4 sm:p-6 space-y-4 sm:space-y-6">
                      {/* Tour Image with Overlay - Mobile Optimized */}
                      <div className="relative group">
                        <img
                          src={tour.images && tour.images.length > 0
                            ? `https://images.unsplash.com/${tour.images[0]}?auto=format&fit=crop&w=400&h=200`
                            : 'https://images.unsplash.com/photo-1516426122078-c23e76319801?auto=format&fit=crop&w=400&h=200'
                          }
                          alt={tour.title}
                          className="w-full h-32 sm:h-40 object-cover rounded-lg sm:rounded-xl transition-transform duration-500 group-hover:scale-105"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent rounded-lg sm:rounded-xl"></div>
                        <div className="absolute bottom-3 sm:bottom-4 left-3 sm:left-4 right-3 sm:right-4">
                          <h3 className="font-cormorant text-lg sm:text-xl font-semibold text-white mb-1 sm:mb-2 leading-tight">{tour.title}</h3>
                        </div>
                      </div>

                      {/* Tour Details - Mobile Optimized */}
                      <div className="space-y-2 sm:space-y-3">
                        <div className="flex items-center justify-between flex-wrap gap-2">
                          <div className="flex items-center gap-2 text-[#A9A9A9]">
                            <Clock className="w-4 h-4 flex-shrink-0" />
                            <span className="font-open-sans text-xs sm:text-sm">{tour.duration}</span>
                          </div>
                          <div className="flex items-center gap-2 text-[#A9A9A9]">
                            <Users className="w-4 h-4 flex-shrink-0" />
                            <span className="font-open-sans text-xs sm:text-sm">Max {tour.maxGroupSize}</span>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Star className="w-4 h-4 text-[#D4C2A4] flex-shrink-0" />
                          <span className="font-open-sans text-xs sm:text-sm text-[#F2EEE6]">
                            {tour.rating || 0} ({tour.reviewCount || 0} reviews)
                          </span>
                        </div>
                      </div>

                      {/* Pricing Breakdown - Mobile Optimized */}
                      <div className="border-t border-[#D4C2A4]/20 pt-4 sm:pt-6 space-y-3 sm:space-y-4">
                        <div className="flex justify-between items-center">
                          <span className="font-open-sans text-xs sm:text-sm text-[#A9A9A9]">Base price per adult</span>
                          <span className="font-open-sans text-xs sm:text-sm font-semibold text-[#F2EEE6]">${tour.price.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="font-open-sans text-xs sm:text-sm text-[#A9A9A9]">Adults</span>
                          <span className="font-open-sans text-xs sm:text-sm text-[#F2EEE6]">{bookingData.groupSize} {bookingData.groupSize === 1 ? 'adult' : 'adults'}</span>
                        </div>
                        {bookingData.childrenCount > 0 && (
                          <div className="flex justify-between items-center">
                            <span className="font-open-sans text-xs sm:text-sm text-[#A9A9A9]">Children (50% off)</span>
                            <span className="font-open-sans text-xs sm:text-sm text-[#F2EEE6]">{bookingData.childrenCount} {bookingData.childrenCount === 1 ? 'child' : 'children'}</span>
                          </div>
                        )}
                        {bookingData.accommodation !== 'budget' && (
                          <div className="flex justify-between items-center">
                            <span className="font-open-sans text-xs sm:text-sm text-[#A9A9A9]">Accommodation upgrade</span>
                            <span className="font-open-sans text-xs sm:text-sm text-[#D4C2A4]">+${bookingData.accommodation === 'standard' ? '200' : '500'}</span>
                          </div>
                        )}
                        {bookingData.childrenCount > 0 && (
                          <div className="flex justify-between items-center">
                            <span className="font-open-sans text-xs sm:text-sm text-green-400">Children discount</span>
                            <span className="font-open-sans text-xs sm:text-sm text-green-400">-${((tour.price + (bookingData.accommodation === 'standard' ? 200 : bookingData.accommodation === 'luxury' ? 500 : 0)) * bookingData.childrenCount * 0.5).toLocaleString()}</span>
                          </div>
                        )}
                        {bookingData.addOns.length > 0 && (
                          <div className="flex justify-between items-center">
                            <span className="font-open-sans text-xs sm:text-sm text-[#A9A9A9]">Add-ons ({bookingData.addOns.length})</span>
                            <span className="font-open-sans text-xs sm:text-sm text-[#D4C2A4]">+${bookingData.addOns.reduce((sum, addon) => {
                              const prices = { photography: 75, cultural: 50, balloon: 550, 'night-drive': 100 };
                              return sum + (prices[addon as keyof typeof prices] || 0);
                            }, 0)}</span>
                          </div>
                        )}

                        {/* Total with luxury styling - Mobile Optimized */}
                        <div className="border-t border-[#D4C2A4]/30 pt-3 sm:pt-4 mt-3 sm:mt-4">
                          <div className="flex justify-between items-center bg-gradient-to-r from-[#D4C2A4]/10 to-transparent p-3 sm:p-4 rounded-lg sm:rounded-xl">
                            <span className="font-cormorant text-lg sm:text-xl font-semibold text-[#F2EEE6]">Total</span>
                            <span className="font-cormorant text-xl sm:text-2xl font-bold text-[#D4C2A4]">${bookingData.totalPrice.toLocaleString()}</span>
                          </div>
                        </div>
                      </div>

                      {/* Start Date Display - Mobile Optimized */}
                      {bookingData.startDate && (
                        <div className="border-t border-[#D4C2A4]/20 pt-4 sm:pt-6">
                          <div className="flex items-center gap-2 sm:gap-3 bg-[#D4C2A4]/10 p-3 sm:p-4 rounded-lg sm:rounded-xl">
                            <Clock className="w-4 h-4 sm:w-5 sm:h-5 text-[#D4C2A4] flex-shrink-0" />
                            <div>
                              <span className="font-open-sans text-xs sm:text-sm text-[#A9A9A9]">Start Date</span>
                              <p className="font-cormorant text-base sm:text-lg font-semibold text-[#F2EEE6]">
                                {bookingData.startDate.toLocaleDateString()}
                              </p>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Tour Highlights - Mobile Optimized */}
                      <div className="border-t border-[#D4C2A4]/20 pt-4 sm:pt-6">
                        <div className="bg-gradient-to-br from-[#D4C2A4]/5 to-transparent p-3 sm:p-5 rounded-lg sm:rounded-xl border border-[#D4C2A4]/10">
                          <h4 className="font-cormorant text-base sm:text-lg font-semibold text-[#F2EEE6] mb-3 sm:mb-4 flex items-center gap-2">
                            <Award className="w-4 h-4 sm:w-5 sm:h-5 text-[#D4C2A4] flex-shrink-0" />
                            Tour Highlights
                          </h4>
                          <ul className="space-y-2">
                            {tour.includes?.slice(0, 4).map((item, index) => (
                              <li key={index} className="flex items-start gap-2 sm:gap-3">
                                <CheckCircle2 className="w-3 h-3 sm:w-4 sm:h-4 text-[#D4C2A4] mt-0.5 flex-shrink-0" />
                                <span className="font-open-sans text-xs sm:text-sm text-[#A9A9A9] leading-relaxed">{item}</span>
                              </li>
                            ))}
                            {tour.includes && tour.includes.length > 4 && (
                              <li className="flex items-center gap-2 sm:gap-3 mt-2 sm:mt-3 pt-2 sm:pt-3 border-t border-[#D4C2A4]/10">
                                <span className="font-open-sans text-xs sm:text-sm font-medium text-[#D4C2A4]">
                                  + {tour.includes.length - 4} more included
                                </span>
                              </li>
                            )}
                          </ul>
                        </div>
                      </div>

                      {/* Cancellation Policy - Mobile Optimized */}
                      <div className="border-t border-[#D4C2A4]/20 pt-4 sm:pt-6">
                        <div className="bg-gradient-to-br from-green-500/10 to-transparent p-3 sm:p-5 rounded-lg sm:rounded-xl border border-green-500/20">
                          <div className="flex items-center gap-2 sm:gap-3 mb-2">
                            <Shield className="w-4 h-4 sm:w-5 sm:h-5 text-green-400 flex-shrink-0" />
                            <span className="font-cormorant text-base sm:text-lg font-semibold text-green-400">Free Cancellation</span>
                          </div>
                          <p className="font-open-sans text-xs sm:text-sm text-[#A9A9A9] leading-relaxed">
                            Cancel up to 30 days before departure for a full refund
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer isDarkBackground={true} />
    </div>
  );
};

export default Booking;
