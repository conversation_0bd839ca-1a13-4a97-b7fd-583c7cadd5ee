
import React, { createContext, useState, useEffect, useContext } from 'react';
import { auth } from '@/lib/firebase';
import { onAuthStateChanged, signInWithEmailAndPassword, createUserWithEmailAndPassword, signOut } from 'firebase/auth';
import { FirebaseService } from '@/services/firebase';
import { UserProfile } from '@/types/firebase';
import { Timestamp } from 'firebase/firestore';
import { withFirebaseErrorHandling, getFirebaseErrorMessage } from '@/utils/firebaseErrorHandler';

interface AuthContextProps {
  currentUser: any;
  userProfile: UserProfile | null;
  loading: boolean;
  showOnboarding: boolean;
  setShowOnboarding: (show: boolean) => void;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, displayName: string) => Promise<void>;
  logout: () => Promise<void>;
  updateUserProfile: (profileData: Partial<UserProfile>) => Promise<void>;
}

const AuthContext = createContext<AuthContextProps>({
  currentUser: null,
  userProfile: null,
  loading: true,
  showOnboarding: false,
  setShowOnboarding: () => {},
  login: async () => {},
  register: async () => {},
  logout: async () => {},
  updateUserProfile: async () => {},
});

export const useAuth = () => useContext(AuthContext);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [showOnboarding, setShowOnboarding] = useState(false);

  const login = async (email: string, password: string) => {
    return withFirebaseErrorHandling(async () => {
      await signInWithEmailAndPassword(auth, email, password);
      // User profile will be loaded by the auth state change listener
    }, 'Firebase Auth - Login');
  };

  const register = async (email: string, password: string, displayName: string) => {
    return withFirebaseErrorHandling(async () => {
      const result = await createUserWithEmailAndPassword(auth, email, password);

      // Create user profile
      const userProfile = {
        uid: result.user.uid,
        email: result.user.email || '',
        displayName: displayName || result.user.email || '',
        role: 'user' as const,
        preferences: {
          accommodation: 'midrange' as const,
          activities: [],
          dietaryRestrictions: [],
          fitnessLevel: 'moderate' as const,
          photographyInterest: false,
          birdingInterest: false
        },
        loyaltyPoints: 0,
        pastBookings: [],
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      };

      const createdProfile = await FirebaseService.createUserProfile(result.user.uid, userProfile);
      setUserProfile(createdProfile);

      // Show onboarding for new users
      setShowOnboarding(true);
    }, 'Firebase Auth - Register');
  };

  const logout = async () => {
    return withFirebaseErrorHandling(async () => {
      await signOut(auth);
    }, 'Firebase Auth - Logout');
  };

  const updateUserProfile = async (profileData: Partial<UserProfile>) => {
    if (!currentUser) throw new Error('Please sign in to update your safari profile.');

    return withFirebaseErrorHandling(async () => {
      await FirebaseService.updateUserProfile(currentUser.uid, profileData);

      // Update local state
      if (userProfile) {
        setUserProfile({ ...userProfile, ...profileData, updatedAt: Timestamp.now() });
      }
    }, 'Firebase Auth - Update Profile');
  };

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (user) {
        setCurrentUser(user);
        
        try {
          const profile = await FirebaseService.getUserProfile(user.uid);
          if (profile) {
            setUserProfile(profile as UserProfile);

            // Check if user should see onboarding
            const onboardingCompleted = localStorage.getItem('onboarding_completed');
            const hasBasicPreferences = profile.preferences?.accommodation &&
                                      profile.preferences?.activities?.length > 0;

            if (!onboardingCompleted && !hasBasicPreferences) {
              setShowOnboarding(true);
            }
          } else {
            // Create user profile if it doesn't exist
            const userProfile = {
              uid: user.uid,
              email: user.email || '',
              displayName: user.displayName || user.email || '',
              role: 'user' as const,
              preferences: {
                accommodation: 'midrange' as const,
                activities: [],
                dietaryRestrictions: [],
                fitnessLevel: 'moderate' as const,
                photographyInterest: false,
                birdingInterest: false
              },
              loyaltyPoints: 0,
              pastBookings: [],
              createdAt: Timestamp.now(),
              updatedAt: Timestamp.now()
            };

            const createdProfile = await FirebaseService.createUserProfile(user.uid, userProfile);
            setUserProfile(createdProfile);
          }
        } catch (error) {
          // Handle profile loading error silently
        }
      } else {
        setCurrentUser(null);
        setUserProfile(null);
      }
      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const value: AuthContextProps = {
    currentUser,
    userProfile,
    loading,
    showOnboarding,
    setShowOnboarding,
    login,
    register,
    logout,
    updateUserProfile
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};
