import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { collection, getDocs, addDoc, updateDoc, deleteDoc, doc, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Plus, Edit, Trash2, Save, X } from 'lucide-react';
import ImageUpload from '@/components/ui/ImageUpload';

interface Destination {
  id?: string;
  name: string;
  description: string;
  country: string;
  region: string;
  coordinates: { lat: number; lng: number };
  bestTimeToVisit: string[];
  climate: string;
  wildlife: Array<{
    species: string;
    scientificName: string;
    category: string;
    abundance: string;
    bestSpottingTime: string;
    behavior: string;
    conservationStatus: string;
    photographyTips: string;
  }>;
  images: string[];
  activities: string[];
  accommodations: string[];
  featured: boolean;
  detailedGuide: {
    overview: string;
    geography: string;
    history: string;
    gettingThere: string;
    accommodation: string;
    packingTips: string[];
    healthSafety: string;
    travelTips: string[];
  };
  conservationInfo: {
    initiatives: string[];
    challenges: string[];
    howTouristsHelp: string[];
    conservationFee: number;
  };
  culturalInfo: {
    tribes: string[];
    languages: string[];
    traditions: string[];
    etiquette: string[];
    culturalSites: string[];
  };
}

const DestinationManagement = () => {
  const [destinations, setDestinations] = useState<Destination[]>([]);
  const [loading, setLoading] = useState(false);
  const [editingDestination, setEditingDestination] = useState<Destination | null>(null);
  const [showForm, setShowForm] = useState(false);
  const { toast } = useToast();

  const initialDestinationState: Destination = {
    name: '',
    description: '',
    country: '',
    region: '',
    coordinates: { lat: 0, lng: 0 },
    bestTimeToVisit: [],
    climate: '',
    wildlife: [],
    images: [],
    activities: [],
    accommodations: [],
    featured: false,
    detailedGuide: {
      overview: '',
      geography: '',
      history: '',
      gettingThere: '',
      accommodation: '',
      packingTips: [],
      healthSafety: '',
      travelTips: []
    },
    conservationInfo: {
      initiatives: [],
      challenges: [],
      howTouristsHelp: [],
      conservationFee: 0
    },
    culturalInfo: {
      tribes: [],
      languages: [],
      traditions: [],
      etiquette: [],
      culturalSites: []
    }
  };

  const [formData, setFormData] = useState<Destination>(initialDestinationState);
  // MODIFICATION: Added new state for raw input strings
  const [arrayInputStrings, setArrayInputStrings] = useState<{ [key: string]: string }>({});

  useEffect(() => {
    fetchDestinations();
  }, []);

  const fetchDestinations = async () => {
    setLoading(true);
    try {
      const querySnapshot = await getDocs(collection(db, 'destinations'));
      const destinationsData = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Destination[];
      setDestinations(destinationsData);
    } catch (error) {
      console.error('Error fetching destinations:', error);
      toast({
        title: "Error",
        description: "Failed to fetch destinations",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    setLoading(true);
    try {
      const destinationData = {
        ...formData,
        updatedAt: Timestamp.now(),
        ...(editingDestination ? {} : { createdAt: Timestamp.now() })
      };

      if (editingDestination && editingDestination.id) {
        await updateDoc(doc(db, 'destinations', editingDestination.id), destinationData);
        toast({
          title: "Success",
          description: "Destination updated successfully",
        });
      } else {
        await addDoc(collection(db, 'destinations'), destinationData);
        toast({
          title: "Success",
          description: "Destination created successfully",
        });
      }

      setShowForm(false);
      setEditingDestination(null);
      setFormData(initialDestinationState);
      setArrayInputStrings({}); // MODIFICATION: Reset string state
      fetchDestinations();
    } catch (error) {
      console.error('Error saving destination:', error);
      toast({
        title: "Error",
        description: "Failed to save destination",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (destinationId: string) => {
    if (!confirm('Are you sure you want to delete this destination?')) return;
    
    setLoading(true);
    try {
      await deleteDoc(doc(db, 'destinations', destinationId));
      toast({
        title: "Success",
        description: "Destination deleted successfully",
      });
      fetchDestinations();
    } catch (error) {
      console.error('Error deleting destination:', error);
      toast({
        title: "Error",
        description: "Failed to delete destination",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (destination: Destination) => {
    setEditingDestination(destination);
    setFormData(destination);
    setShowForm(true);

    // MODIFICATION: Initialize the string state from existing data
    const initialStrings = {
      bestTimeToVisit: destination.bestTimeToVisit.join(', '),
      activities: destination.activities.join(', '),
      accommodations: destination.accommodations.join(', '),
      'detailedGuide.packingTips': destination.detailedGuide.packingTips.join(', '),
      'detailedGuide.travelTips': destination.detailedGuide.travelTips.join(', '),
      'conservationInfo.initiatives': destination.conservationInfo.initiatives.join(', '),
      'conservationInfo.challenges': destination.conservationInfo.challenges.join(', '),
      'conservationInfo.howTouristsHelp': destination.conservationInfo.howTouristsHelp.join(', '),
      'culturalInfo.tribes': destination.culturalInfo.tribes.join(', '),
      'culturalInfo.languages': destination.culturalInfo.languages.join(', '),
      'culturalInfo.traditions': destination.culturalInfo.traditions.join(', '),
      'culturalInfo.etiquette': destination.culturalInfo.etiquette.join(', '),
      'culturalInfo.culturalSites': destination.culturalInfo.culturalSites.join(', '),
    };
    setArrayInputStrings(initialStrings);
  };
  
  // MODIFICATION: Replaced `handleArrayInput` with this new handler
  const handleArrayInputChange = (field: string, value: string, nestedField?: string) => {
    const key = nestedField ? `${field}.${nestedField}` : field;
    
    // Update the local string state for a smooth typing experience
    setArrayInputStrings(prev => ({ ...prev, [key]: value }));
  
    // Update the actual formData state with the processed array
    const arrayValue = value.split(',').map(item => item.trim()).filter(item => item);
    
    if (nestedField) {
      setFormData(prev => ({
        ...prev,
        [field]: {
          ...(prev[field as keyof Destination] as any),
          [nestedField]: arrayValue
        }
      }));
    } else {
      setFormData(prev => ({ ...prev, [field]: arrayValue }));
    }
  };

  if (showForm) {
    return (
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>{editingDestination ? 'Edit Destination' : 'Create New Destination'}</CardTitle>
          <CardDescription>
            Fill out all the details for the safari destination
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <Tabs defaultValue="basic" className="w-full">
            <div className="overflow-x-auto mb-4">
              <TabsList className="grid w-full min-w-[500px] md:min-w-0 grid-cols-5 h-auto">
                <TabsTrigger value="basic" className="text-xs md:text-sm py-2 px-2 md:px-3">Basic Info</TabsTrigger>
                <TabsTrigger value="wildlife" className="text-xs md:text-sm py-2 px-2 md:px-3">Wildlife</TabsTrigger>
                <TabsTrigger value="guide" className="text-xs md:text-sm py-2 px-2 md:px-3">Travel Guide</TabsTrigger>
                <TabsTrigger value="conservation" className="text-xs md:text-sm py-2 px-2 md:px-3">Conservation</TabsTrigger>
                <TabsTrigger value="cultural" className="text-xs md:text-sm py-2 px-2 md:px-3">Cultural</TabsTrigger>
              </TabsList>
            </div>
            
            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Destination Name</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Serengeti National Park"
                  />
                </div>
                <div>
                  <Label htmlFor="country">Country</Label>
                  <Input
                    id="country"
                    value={formData.country}
                    onChange={(e) => setFormData(prev => ({ ...prev, country: e.target.value }))}
                    placeholder="Tanzania"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="World-famous for the Great Migration and abundant wildlife"
                  rows={4}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="region">Region</Label>
                  <Input
                    id="region"
                    value={formData.region}
                    onChange={(e) => setFormData(prev => ({ ...prev, region: e.target.value }))}
                    placeholder="Northern Tanzania"
                  />
                </div>
                <div>
                  <Label htmlFor="climate">Climate</Label>
                  <Input
                    id="climate"
                    value={formData.climate}
                    onChange={(e) => setFormData(prev => ({ ...prev, climate: e.target.value }))}
                    placeholder="Tropical savanna climate"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="lat">Latitude</Label>
                  <Input
                    id="lat"
                    type="number"
                    step="any"
                    value={formData.coordinates.lat}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      coordinates: { ...prev.coordinates, lat: Number(e.target.value) }
                    }))}
                  />
                </div>
                <div>
                  <Label htmlFor="lng">Longitude</Label>
                  <Input
                    id="lng"
                    type="number"
                    step="any"
                    value={formData.coordinates.lng}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      coordinates: { ...prev.coordinates, lng: Number(e.target.value) }
                    }))}
                  />
                </div>
              </div>

              {/* MODIFICATION: Updated value and onChange for this block */}
              <div>
                <Label htmlFor="bestTimeToVisit">Best Time to Visit (comma-separated months)</Label>
                <Input
                  id="bestTimeToVisit"
                  value={arrayInputStrings.bestTimeToVisit || ''}
                  onChange={(e) => handleArrayInputChange('bestTimeToVisit', e.target.value)}
                  placeholder="June, July, August, September, October"
                />
              </div>

              <div>
                <Label htmlFor="activities">Activities (comma-separated)</Label>
                <Input
                  id="activities"
                  value={arrayInputStrings.activities || ''}
                  onChange={(e) => handleArrayInputChange('activities', e.target.value)}
                  placeholder="Game Drives, Hot Air Balloon Safari, Walking Safari"
                />
              </div>

              <div>
                <Label htmlFor="accommodations">Accommodations (comma-separated)</Label>
                <Input
                  id="accommodations"
                  value={arrayInputStrings.accommodations || ''}
                  onChange={(e) => handleArrayInputChange('accommodations', e.target.value)}
                  placeholder="Luxury Lodge, Tented Camp, Mobile Camp"
                />
              </div>

              <ImageUpload
                value={formData.images}
                onChange={(value) => setFormData(prev => ({ ...prev, images: value as string[] }))}
                label="Destination Images"
                placeholder="Enter image URL or upload files"
                folder="destinations"
                multiple
                maxFiles={8}
                disabled={loading}
              />

              <div className="flex items-center space-x-2">
                <Switch
                  checked={formData.featured}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, featured: checked }))}
                />
                <Label>Featured Destination</Label>
              </div>
            </TabsContent>

            <TabsContent value="wildlife" className="space-y-4">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold">Wildlife Species</h3>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const newWildlife = {
                        species: '',
                        scientificName: '',
                        category: '',
                        abundance: '',
                        bestSpottingTime: '',
                        behavior: '',
                        conservationStatus: '',
                        photographyTips: ''
                      };
                      setFormData(prev => ({
                        ...prev,
                        wildlife: [...prev.wildlife, newWildlife]
                      }));
                    }}
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Add Wildlife Species
                  </Button>
                </div>

                {formData.wildlife.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    No wildlife species added yet. Click "Add Wildlife Species" to get started.
                  </div>
                ) : (
                  <div className="space-y-6">
                    {formData.wildlife.map((animal, index) => (
                      <Card key={index} className="p-4">
                        <div className="flex justify-between items-start mb-4">
                          <h4 className="font-medium">Wildlife Species #{index + 1}</h4>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              const updatedWildlife = formData.wildlife.filter((_, i) => i !== index);
                              setFormData(prev => ({ ...prev, wildlife: updatedWildlife }));
                            }}
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor={`species-${index}`}>Species Name</Label>
                            <Input
                              id={`species-${index}`}
                              value={animal.species}
                              onChange={(e) => {
                                const updatedWildlife = [...formData.wildlife];
                                updatedWildlife[index] = { ...updatedWildlife[index], species: e.target.value };
                                setFormData(prev => ({ ...prev, wildlife: updatedWildlife }));
                              }}
                              placeholder="African Lion"
                            />
                          </div>

                          <div>
                            <Label htmlFor={`scientificName-${index}`}>Scientific Name</Label>
                            <Input
                              id={`scientificName-${index}`}
                              value={animal.scientificName}
                              onChange={(e) => {
                                const updatedWildlife = [...formData.wildlife];
                                updatedWildlife[index] = { ...updatedWildlife[index], scientificName: e.target.value };
                                setFormData(prev => ({ ...prev, wildlife: updatedWildlife }));
                              }}
                              placeholder="Panthera leo"
                            />
                          </div>

                          <div>
                            <Label htmlFor={`category-${index}`}>Category</Label>
                            <Input
                              id={`category-${index}`}
                              value={animal.category}
                              onChange={(e) => {
                                const updatedWildlife = [...formData.wildlife];
                                updatedWildlife[index] = { ...updatedWildlife[index], category: e.target.value };
                                setFormData(prev => ({ ...prev, wildlife: updatedWildlife }));
                              }}
                              placeholder="big-five, predator, herbivore, etc."
                            />
                          </div>

                          <div>
                            <Label htmlFor={`abundance-${index}`}>Abundance</Label>
                            <Input
                              id={`abundance-${index}`}
                              value={animal.abundance}
                              onChange={(e) => {
                                const updatedWildlife = [...formData.wildlife];
                                updatedWildlife[index] = { ...updatedWildlife[index], abundance: e.target.value };
                                setFormData(prev => ({ ...prev, wildlife: updatedWildlife }));
                              }}
                              placeholder="common, moderate, rare"
                            />
                          </div>

                          <div>
                            <Label htmlFor={`bestSpottingTime-${index}`}>Best Spotting Time</Label>
                            <Input
                              id={`bestSpottingTime-${index}`}
                              value={animal.bestSpottingTime}
                              onChange={(e) => {
                                const updatedWildlife = [...formData.wildlife];
                                updatedWildlife[index] = { ...updatedWildlife[index], bestSpottingTime: e.target.value };
                                setFormData(prev => ({ ...prev, wildlife: updatedWildlife }));
                              }}
                              placeholder="Early morning and evening"
                            />
                          </div>

                          <div>
                            <Label htmlFor={`conservationStatus-${index}`}>Conservation Status</Label>
                            <Input
                              id={`conservationStatus-${index}`}
                              value={animal.conservationStatus}
                              onChange={(e) => {
                                const updatedWildlife = [...formData.wildlife];
                                updatedWildlife[index] = { ...updatedWildlife[index], conservationStatus: e.target.value };
                                setFormData(prev => ({ ...prev, wildlife: updatedWildlife }));
                              }}
                              placeholder="Vulnerable, Endangered, Least Concern"
                            />
                          </div>
                        </div>

                        <div className="mt-4 space-y-4">
                          <div>
                            <Label htmlFor={`behavior-${index}`}>Behavior</Label>
                            <Textarea
                              id={`behavior-${index}`}
                              value={animal.behavior}
                              onChange={(e) => {
                                const updatedWildlife = [...formData.wildlife];
                                updatedWildlife[index] = { ...updatedWildlife[index], behavior: e.target.value };
                                setFormData(prev => ({ ...prev, wildlife: updatedWildlife }));
                              }}
                              placeholder="Social behavior, hunting patterns, habitat preferences..."
                              rows={2}
                            />
                          </div>

                          <div>
                            <Label htmlFor={`photographyTips-${index}`}>Photography Tips</Label>
                            <Textarea
                              id={`photographyTips-${index}`}
                              value={animal.photographyTips}
                              onChange={(e) => {
                                const updatedWildlife = [...formData.wildlife];
                                updatedWildlife[index] = { ...updatedWildlife[index], photographyTips: e.target.value };
                                setFormData(prev => ({ ...prev, wildlife: updatedWildlife }));
                              }}
                              placeholder="Best camera settings, positioning tips, lighting recommendations..."
                              rows={2}
                            />
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="guide" className="space-y-4">
              <div>
                <Label htmlFor="overview">Overview</Label>
                <Textarea
                  id="overview"
                  value={formData.detailedGuide.overview}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    detailedGuide: { ...prev.detailedGuide, overview: e.target.value }
                  }))}
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="geography">Geography</Label>
                  <Textarea
                    id="geography"
                    value={formData.detailedGuide.geography}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      detailedGuide: { ...prev.detailedGuide, geography: e.target.value }
                    }))}
                    rows={3}
                  />
                </div>
                <div>
                  <Label htmlFor="history">History</Label>
                  <Textarea
                    id="history"
                    value={formData.detailedGuide.history}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      detailedGuide: { ...prev.detailedGuide, history: e.target.value }
                    }))}
                    rows={3}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="gettingThere">Getting There</Label>
                <Textarea
                  id="gettingThere"
                  value={formData.detailedGuide.gettingThere}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    detailedGuide: { ...prev.detailedGuide, gettingThere: e.target.value }
                  }))}
                  rows={2}
                />
              </div>

              {/* MODIFICATION: Updated value and onChange for this block */}
              <div>
                <Label htmlFor="packingTips">Packing Tips (comma-separated)</Label>
                <Input
                  id="packingTips"
                  value={arrayInputStrings['detailedGuide.packingTips'] || ''}
                  onChange={(e) => handleArrayInputChange('detailedGuide', e.target.value, 'packingTips')}
                  placeholder="Neutral colored clothing, Good camera, Binoculars"
                />
              </div>

              <div>
                <Label htmlFor="healthSafety">Health & Safety</Label>
                <Textarea
                  id="healthSafety"
                  value={formData.detailedGuide.healthSafety}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    detailedGuide: { ...prev.detailedGuide, healthSafety: e.target.value }
                  }))}
                  rows={2}
                />
              </div>

              <div>
                <Label htmlFor="travelTips">Travel Tips (comma-separated)</Label>
                <Input
                  id="travelTips"
                  value={arrayInputStrings['detailedGuide.travelTips'] || ''}
                  onChange={(e) => handleArrayInputChange('detailedGuide', e.target.value, 'travelTips')}
                  placeholder="Book in advance, Respect wildlife, Follow guide instructions"
                />
              </div>
            </TabsContent>

            <TabsContent value="conservation" className="space-y-4">
              <div>
                <Label htmlFor="conservationFee">Conservation Fee ($)</Label>
                <Input
                  id="conservationFee"
                  type="number"
                  value={formData.conservationInfo.conservationFee}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    conservationInfo: { ...prev.conservationInfo, conservationFee: Number(e.target.value) }
                  }))}
                />
              </div>

              {/* MODIFICATION: Updated value and onChange for this block */}
              <div>
                <Label htmlFor="initiatives">Conservation Initiatives (comma-separated)</Label>
                <Input
                  id="initiatives"
                  value={arrayInputStrings['conservationInfo.initiatives'] || ''}
                  onChange={(e) => handleArrayInputChange('conservationInfo', e.target.value, 'initiatives')}
                  placeholder="Anti-poaching programs, Community conservation"
                />
              </div>

              <div>
                <Label htmlFor="challenges">Conservation Challenges (comma-separated)</Label>
                <Input
                  id="challenges"
                  value={arrayInputStrings['conservationInfo.challenges'] || ''}
                  onChange={(e) => handleArrayInputChange('conservationInfo', e.target.value, 'challenges')}
                  placeholder="Human-wildlife conflict, Climate change"
                />
              </div>

              <div>
                <Label htmlFor="howTouristsHelp">How Tourists Help (comma-separated)</Label>
                <Input
                  id="howTouristsHelp"
                  value={arrayInputStrings['conservationInfo.howTouristsHelp'] || ''}
                  onChange={(e) => handleArrayInputChange('conservationInfo', e.target.value, 'howTouristsHelp')}
                  placeholder="Park fees fund conservation, Employment for locals"
                />
              </div>
            </TabsContent>

            <TabsContent value="cultural" className="space-y-4">
              {/* MODIFICATION: Updated value and onChange for this block */}
              <div>
                <Label htmlFor="tribes">Local Tribes (comma-separated)</Label>
                <Input
                  id="tribes"
                  value={arrayInputStrings['culturalInfo.tribes'] || ''}
                  onChange={(e) => handleArrayInputChange('culturalInfo', e.target.value, 'tribes')}
                  placeholder="Maasai"
                />
              </div>

              <div>
                <Label htmlFor="languages">Languages (comma-separated)</Label>
                <Input
                  id="languages"
                  value={arrayInputStrings['culturalInfo.languages'] || ''}
                  onChange={(e) => handleArrayInputChange('culturalInfo', e.target.value, 'languages')}
                  placeholder="Swahili, English, Maasai"
                />
              </div>

              <div>
                <Label htmlFor="traditions">Traditions (comma-separated)</Label>
                <Input
                  id="traditions"
                  value={arrayInputStrings['culturalInfo.traditions'] || ''}
                  onChange={(e) => handleArrayInputChange('culturalInfo', e.target.value, 'traditions')}
                  placeholder="Traditional dances, Beadwork, Cattle herding"
                />
              </div>

              <div>
                <Label htmlFor="etiquette">Cultural Etiquette (comma-separated)</Label>
                <Input
                  id="etiquette"
                  value={arrayInputStrings['culturalInfo.etiquette'] || ''}
                  onChange={(e) => handleArrayInputChange('culturalInfo', e.target.value, 'etiquette')}
                  placeholder="Respect local customs, Ask before photographing people"
                />
              </div>

              <div>
                <Label htmlFor="culturalSites">Cultural Sites (comma-separated)</Label>
                <Input
                  id="culturalSites"
                  value={arrayInputStrings['culturalInfo.culturalSites'] || ''}
                  onChange={(e) => handleArrayInputChange('culturalInfo', e.target.value, 'culturalSites')}
                  placeholder="Maasai villages, Rock paintings"
                />
              </div>
            </TabsContent>
          </Tabs>

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              variant="outline"
              onClick={() => {
                setShowForm(false);
                setEditingDestination(null);
                setFormData(initialDestinationState);
                setArrayInputStrings({}); // MODIFICATION: Reset string state
              }}
            >
              <X className="w-4 h-4 mr-2" />
              Cancel
            </Button>
            <Button onClick={handleSave} disabled={loading}>
              <Save className="w-4 h-4 mr-2" />
              {loading ? 'Saving...' : 'Save Destination'}
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Destination Management</CardTitle>
            <CardDescription>Manage safari destinations and parks</CardDescription>
          </div>
          <Button onClick={() => setShowForm(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Add New Destination
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="text-center py-4">Loading destinations...</div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="min-w-[150px]">Name</TableHead>
                  <TableHead className="min-w-[100px]">Country</TableHead>
                  <TableHead className="min-w-[100px]">Region</TableHead>
                  <TableHead className="min-w-[120px]">Conservation Fee</TableHead>
                  <TableHead className="min-w-[80px]">Featured</TableHead>
                  <TableHead className="min-w-[100px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
            <TableBody>
              {destinations.map((destination) => (
                <TableRow key={destination.id}>
                  <TableCell className="font-medium">{destination.name}</TableCell>
                  <TableCell>{destination.country}</TableCell>
                  <TableCell>{destination.region}</TableCell>
                  <TableCell>${destination.conservationInfo.conservationFee}</TableCell>
                  <TableCell>
                    {destination.featured && <Badge variant="outline">Featured</Badge>}
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(destination)}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => destination.id && handleDelete(destination.id)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default DestinationManagement;