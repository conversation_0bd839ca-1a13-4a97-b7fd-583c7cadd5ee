// Filtering System Test Utility
// This file provides functions to test the new filtering system

import { Tour } from '@/types/firebase';

// Mock tour data for testing
const mockTours: Tour[] = [
  {
    id: '1',
    title: 'Serengeti Wildlife Safari',
    description: 'Experience the Big Five in Serengeti',
    price: 2500,
    duration: '7 days',
    location: 'Serengeti',
    tourType: 'wildlife',
    accommodationLevel: 'luxury',
    destinations: ['Serengeti', 'Ngorongoro'],
    activities: ['Game Drive', 'Photography'],
    accommodations: ['Luxury Lodge'],
    includes: ['Meals', 'Transport'],
    excludes: ['Flights'],
    images: ['serengeti1.jpg'],
    maxGroupSize: 8,
    minGroupSize: 2,
    category: 'Wildlife Safari',
    featured: true,
    availability: true,
    seasonality: {
      bestMonths: ['June', 'July', 'August'],
      weatherInfo: 'Dry season'
    },
    specialFeatures: ['Big Five', 'Great Migration'],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: '2',
    title: 'Maasai Cultural Experience',
    description: 'Immerse in authentic Maasai culture',
    price: 1200,
    duration: '3 days',
    location: 'Maasai Villages',
    tourType: 'cultural',
    accommodationLevel: 'mid-range',
    destinations: ['Maasai Villages'],
    activities: ['Cultural Exchange', 'Traditional Ceremonies'],
    accommodations: ['Cultural Lodge'],
    includes: ['Meals', 'Cultural Guide'],
    excludes: ['Flights'],
    images: ['maasai1.jpg'],
    maxGroupSize: 12,
    minGroupSize: 4,
    category: 'Cultural',
    featured: false,
    availability: true,
    seasonality: {
      bestMonths: ['All Year'],
      weatherInfo: 'Year-round availability'
    },
    specialFeatures: ['Authentic Experience'],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: '3',
    title: 'Mount Kilimanjaro Climbing',
    description: 'Conquer Africa\'s highest peak',
    price: 3500,
    duration: '8 days',
    location: 'Mount Kilimanjaro',
    tourType: 'kilimanjaro',
    accommodationLevel: 'budget',
    destinations: ['Mount Kilimanjaro'],
    activities: ['Mountain Climbing', 'Trekking'],
    accommodations: ['Mountain Huts'],
    includes: ['Guide', 'Equipment'],
    excludes: ['Personal Gear'],
    images: ['kilimanjaro1.jpg'],
    maxGroupSize: 6,
    minGroupSize: 2,
    category: 'Climbing',
    featured: true,
    availability: true,
    seasonality: {
      bestMonths: ['January', 'February', 'September'],
      weatherInfo: 'Best climbing conditions'
    },
    specialFeatures: ['Uhuru Peak'],
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Test filter functions
export function testTourTypeFilter(tourType: string): { passed: boolean; results: Tour[]; message: string } {
  const filtered = mockTours.filter(tour => {
    if (tourType === 'all') return true;
    return tour.tourType === tourType;
  });

  const expectedCounts = {
    'all': 3,
    'wildlife': 1,
    'cultural': 1,
    'kilimanjaro': 1
  };

  const expected = expectedCounts[tourType as keyof typeof expectedCounts] || 0;
  const passed = filtered.length === expected;

  return {
    passed,
    results: filtered,
    message: `TourType filter '${tourType}': Expected ${expected}, got ${filtered.length} tours`
  };
}

export function testPriceRangeFilter(minPrice: number, maxPrice: number): { passed: boolean; results: Tour[]; message: string } {
  const filtered = mockTours.filter(tour => 
    tour.price >= minPrice && tour.price <= maxPrice
  );

  const withinRange = filtered.every(tour => 
    tour.price >= minPrice && tour.price <= maxPrice
  );

  return {
    passed: withinRange,
    results: filtered,
    message: `Price range filter [$${minPrice}-$${maxPrice}]: ${filtered.length} tours found, all within range: ${withinRange}`
  };
}

export function testAccommodationFilter(level: string): { passed: boolean; results: Tour[]; message: string } {
  const filtered = mockTours.filter(tour => {
    if (level === 'all') return true;
    return tour.accommodationLevel?.toLowerCase() === level.toLowerCase();
  });

  const correctLevel = filtered.every(tour => 
    level === 'all' || tour.accommodationLevel?.toLowerCase() === level.toLowerCase()
  );

  return {
    passed: correctLevel,
    results: filtered,
    message: `Accommodation filter '${level}': ${filtered.length} tours found, all correct level: ${correctLevel}`
  };
}

export function testDurationFilter(duration: string): { passed: boolean; results: Tour[]; message: string } {
  const filtered = mockTours.filter(tour => {
    if (duration === 'all') return true;
    
    const tourDurationDays = parseInt(tour.duration.match(/\d+/)?.[0] || '0');
    switch (duration) {
      case '1-3 days':
        return tourDurationDays >= 1 && tourDurationDays <= 3;
      case '4-7 days':
        return tourDurationDays >= 4 && tourDurationDays <= 7;
      case '8+ days':
        return tourDurationDays >= 8;
      default:
        return true;
    }
  });

  return {
    passed: true, // We'll assume the logic is correct if it runs without error
    results: filtered,
    message: `Duration filter '${duration}': ${filtered.length} tours found`
  };
}

export function testDestinationFilter(destinations: string[]): { passed: boolean; results: Tour[]; message: string } {
  const filtered = mockTours.filter(tour => {
    if (destinations.length === 0) return true;
    
    return destinations.some(dest => 
      tour.destinations.some(tourDest => 
        tourDest.toLowerCase().includes(dest.toLowerCase())
      )
    );
  });

  return {
    passed: true,
    results: filtered,
    message: `Destination filter [${destinations.join(', ')}]: ${filtered.length} tours found`
  };
}

// Comprehensive test function
export function runFilteringTests(): { passed: number; failed: number; results: string[] } {
  const results: string[] = [];
  let passed = 0;
  let failed = 0;

  // Test tour type filters
  const tourTypes = ['all', 'wildlife', 'cultural', 'kilimanjaro'];
  tourTypes.forEach(type => {
    const test = testTourTypeFilter(type);
    if (test.passed) {
      results.push(`✅ ${test.message}`);
      passed++;
    } else {
      results.push(`❌ ${test.message}`);
      failed++;
    }
  });

  // Test price range filters
  const priceRanges = [
    [0, 10000],
    [1000, 2000],
    [2000, 4000]
  ];
  priceRanges.forEach(([min, max]) => {
    const test = testPriceRangeFilter(min, max);
    if (test.passed) {
      results.push(`✅ ${test.message}`);
      passed++;
    } else {
      results.push(`❌ ${test.message}`);
      failed++;
    }
  });

  // Test accommodation filters
  const accommodationLevels = ['all', 'budget', 'mid-range', 'luxury'];
  accommodationLevels.forEach(level => {
    const test = testAccommodationFilter(level);
    if (test.passed) {
      results.push(`✅ ${test.message}`);
      passed++;
    } else {
      results.push(`❌ ${test.message}`);
      failed++;
    }
  });

  // Test duration filters
  const durations = ['all', '1-3 days', '4-7 days', '8+ days'];
  durations.forEach(duration => {
    const test = testDurationFilter(duration);
    if (test.passed) {
      results.push(`✅ ${test.message}`);
      passed++;
    } else {
      results.push(`❌ ${test.message}`);
      failed++;
    }
  });

  // Test destination filters
  const destinationTests = [
    [],
    ['Serengeti'],
    ['Maasai Villages'],
    ['Mount Kilimanjaro']
  ];
  destinationTests.forEach(destinations => {
    const test = testDestinationFilter(destinations);
    if (test.passed) {
      results.push(`✅ ${test.message}`);
      passed++;
    } else {
      results.push(`❌ ${test.message}`);
      failed++;
    }
  });

  return { passed, failed, results };
}

// Export for console testing
export function logFilteringTestResults(): void {
  console.log('🔧 Running Filtering System Tests...\n');
  const { passed, failed, results } = runFilteringTests();
  
  results.forEach(result => console.log(result));
  
  console.log(`\n📊 Test Results: ${passed} passed, ${failed} failed`);
  
  if (failed === 0) {
    console.log('🎉 All filtering tests passed!');
  } else {
    console.log('⚠️ Some tests failed. Please review the filtering system.');
  }
}

// Test URL parameter integration
export function testURLParameterIntegration(): void {
  console.log('🔗 Testing URL Parameter Integration...\n');
  
  const testCases = [
    { url: '/tours?tourType=wildlife', expected: 'wildlife' },
    { url: '/tours?tourType=cultural', expected: 'cultural' },
    { url: '/tours?tourType=kilimanjaro', expected: 'kilimanjaro' },
    { url: '/tours', expected: null }
  ];
  
  testCases.forEach(({ url, expected }) => {
    const urlParams = new URLSearchParams(url.split('?')[1] || '');
    const tourType = urlParams.get('tourType');
    
    if (tourType === expected) {
      console.log(`✅ URL ${url} correctly extracts tourType: ${tourType || 'null'}`);
    } else {
      console.log(`❌ URL ${url} expected ${expected}, got ${tourType}`);
    }
  });
  
  console.log('\n🎯 URL parameter integration test complete!');
}
