import React from 'react';
import { Link } from 'react-router-dom';

const LastSection: React.FC = () => {
  return (
    <section
      className="relative isolate flex items-center justify-center w-full min-h-[720px] sm:min-h-[840px] lg:min-h-screen bg-no-repeat bg-cover bg-center"
      style={{
        backgroundImage:
          "url('https://firebasestorage.googleapis.com/v0/b/warriorsofafricasafari-5bb0d.firebasestorage.app/o/imagez%2Fballonz.webp?alt=media&token=5113681e-648f-4103-a4d9-0205d135726d')",
      }}
      aria-label="Custom Safari"
    >
      {/* <PERSON> overlays to match the darkened photo look */}
      <div className="absolute inset-0 bg-[#0A0D10]/30" />
      <div className="absolute inset-0 bg-gradient-to-b from-black/40 via-[#0B0E11]/60 to-[#0B0E11]/80" />

      <div className="relative z-10 w-full max-w-6xl px-4 sm:px-6 lg:px-8">
        {/* Dark card */}
        <div className="mx-auto max-w-4xl rounded-xl sm:rounded-2xl bg-[#111418]/90 ring-1 ring-white/5 shadow-[0_30px_80px_-20px_rgba(0,0,0,0.65)] p-6 sm:p-9 md:p-12">
          <p className="font-open-sans uppercase text-[10px] sm:text-xs tracking-[0.28em] text-[#C0B6A9]/70 mb-3">
            Custom Safari
          </p>

          <h1 className="font-cormorant text-[#F2EEE6] leading-tight text-3xl sm:text-4xl md:text-5xl lg:text-6xl mb-5">
            Let’s Transform your <em className="italic text-[#C9713E]">Dream Safari</em> into Reality
          </h1>

          <p className="font-open-sans text-sm sm:text-base md:text-[17px] leading-relaxed text-[#E1DED7]/85 mb-8">
            Got a vision for your safari? We’re all ears! Tell us your must-haves—maybe a private game
            drive in Ngorongoro or a beach escape in Zanzibar—and we’ll craft an adventure that’s 100% you.
            Our expert planners know Tanzania like the back of their hand, so you’ll get the best spots,
            top-notch guides, and all the luxury you deserve.
          </p>

          {/* CTA row: filled button left, text link right */}
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <Link
              to="/tour-builder"
              className="inline-flex h-11 items-center justify-center rounded-md bg-[#C9713E] px-8 text-[12px] font-open-sans font-semibold uppercase tracking-[0.18em] text-white transition-colors hover:bg-[#D6804D] focus:outline-none focus-visible:ring-2 focus-visible:ring-[#C9713E]/50"
            >
              Plan your safari
            </Link>

            <Link
              to="/contact"
              className="inline-flex h-11 items-center justify-center text-[#C9713E] text-[12px] font-open-sans uppercase tracking-[0.22em] transition-colors hover:text-[#E08A55]"
            >
              Call our expert
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default LastSection;