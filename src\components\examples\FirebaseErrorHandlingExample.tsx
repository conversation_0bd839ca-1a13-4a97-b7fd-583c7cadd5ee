/**
 * Example component demonstrating the new Firebase error handling system
 * This shows how Firebase errors are now masked with custom Warriors Safari messages
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useFirebaseAuth, useFirebaseFirestore, useFirebaseStorage } from '@/hooks/useFirebaseError';
import { FirebaseService } from '@/services/firebase';
import { TourService } from '@/services/tourService';
import { BookingService } from '@/services/bookingService';
import { useAuth } from '@/contexts/AuthContext';

const FirebaseErrorHandlingExample: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const { login, register } = useAuth();
  
  // Use specialized error handlers for different Firebase operations
  const authErrorHandler = useFirebaseAuth();
  const firestoreErrorHandler = useFirebaseFirestore();
  const storageErrorHandler = useFirebaseStorage();

  // Example: Authentication with custom error handling
  const handleTestLogin = async () => {
    setLoading(true);
    try {
      // This will show custom Warriors Safari error messages instead of Firebase errors
      await login('<EMAIL>', 'wrongpassword');
    } catch (error) {
      // Error is already handled by the AuthContext with custom messages
      console.log('Login failed with custom message:', error.message);
    } finally {
      setLoading(false);
    }
  };

  // Example: Firestore operation with custom error handling
  const handleTestFirestore = async () => {
    setLoading(true);
    
    // Using the hook for automatic error handling with toast
    const result = await firestoreErrorHandler.handleAsyncOperation(
      () => FirebaseService.getTour('nonexistent-tour-id'),
      'Testing Firestore Error Handling'
    );
    
    if (!result) {
      console.log('Operation failed, but user saw a friendly message');
    }
    
    setLoading(false);
  };

  // Example: Storage operation with custom error handling
  const handleTestStorage = async () => {
    setLoading(true);
    
    // Create a fake file to test upload error
    const fakeFile = new File(['test'], 'test.txt', { type: 'text/plain' });
    
    const result = await storageErrorHandler.handleAsyncOperation(
      () => FirebaseService.uploadImage(fakeFile, 'test-folder'),
      'Testing Storage Error Handling'
    );
    
    if (!result) {
      console.log('Upload failed, but user saw a friendly message');
    }
    
    setLoading(false);
  };

  // Example: Service layer error handling
  const handleTestTourService = async () => {
    setLoading(true);
    try {
      // This will automatically use the custom error handling from TourService
      await TourService.getTour('nonexistent-tour');
    } catch (error) {
      console.log('Tour service error with custom message:', error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Firebase Error Handling Test</CardTitle>
          <CardDescription>
            Test the new custom Firebase error handling system. All Firebase errors 
            are now masked with Warriors Safari-themed messages.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button 
              onClick={handleTestLogin}
              disabled={loading}
              variant="outline"
            >
              Test Auth Error (Login)
            </Button>
            
            <Button 
              onClick={handleTestFirestore}
              disabled={loading}
              variant="outline"
            >
              Test Firestore Error
            </Button>
            
            <Button 
              onClick={handleTestStorage}
              disabled={loading}
              variant="outline"
            >
              Test Storage Error
            </Button>
            
            <Button 
              onClick={handleTestTourService}
              disabled={loading}
              variant="outline"
            >
              Test Service Layer Error
            </Button>
          </div>
          
          <div className="mt-6 p-4 bg-muted rounded-lg">
            <h4 className="font-semibold mb-2">What's Changed:</h4>
            <ul className="text-sm space-y-1 list-disc list-inside">
              <li>All Firebase errors now show custom Warriors Safari messages</li>
              <li>No more technical Firebase error codes visible to users</li>
              <li>Consistent error handling across all Firebase operations</li>
              <li>Automatic error tracking for analytics</li>
              <li>Support contact information included for critical errors</li>
            </ul>
          </div>
          
          <div className="mt-4 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-semibold mb-2 text-blue-800">Example Error Messages:</h4>
            <ul className="text-sm space-y-1 text-blue-700">
              <li><strong>Before:</strong> "auth/user-not-found"</li>
              <li><strong>After:</strong> "We couldn't find an account with that email. Please check your email or create a new account to start your safari adventure."</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default FirebaseErrorHandlingExample;
