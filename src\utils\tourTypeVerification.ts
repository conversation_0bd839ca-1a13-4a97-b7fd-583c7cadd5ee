// Tour Type Verification Utility
// This file helps verify that the tour type system is working correctly

import { Tour } from '@/types/firebase';

// Valid tour types according to our new system
export const VALID_TOUR_TYPES = ['kilimanjaro', 'wildlife', 'cultural'] as const;
export type ValidTourType = typeof VALID_TOUR_TYPES[number];

// Function to validate if a tour type is valid
export function isValidTourType(tourType: string): tourType is ValidTourType {
  return VALID_TOUR_TYPES.includes(tourType as ValidTourType);
}

// Function to get display name for tour type
export function getTourTypeDisplayName(tourType: ValidTourType): string {
  switch (tourType) {
    case 'kilimanjaro':
      return 'Kilimanjaro Climbing';
    case 'wildlife':
      return 'Wildlife Safari';
    case 'cultural':
      return 'Cultural Experience';
    default:
      return 'Unknown Tour Type';
  }
}

// Function to validate a tour object
export function validateTour(tour: Partial<Tour>): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Check if tour type is valid
  if (!tour.tourType) {
    errors.push('Tour type is required');
  } else if (!isValidTourType(tour.tourType)) {
    errors.push(`Invalid tour type: ${tour.tourType}. Valid types are: ${VALID_TOUR_TYPES.join(', ')}`);
  }

  // Check other required fields
  if (!tour.title) {
    errors.push('Tour title is required');
  }

  if (!tour.description) {
    errors.push('Tour description is required');
  }

  if (typeof tour.price !== 'number' || tour.price <= 0) {
    errors.push('Tour price must be a positive number');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

// Function to create a sample tour for testing
export function createSampleTour(tourType: ValidTourType): Tour {
  const baseTour = {
    id: `sample-${tourType}-${Date.now()}`,
    title: `Sample ${getTourTypeDisplayName(tourType)}`,
    description: `A sample ${tourType} tour for testing purposes`,
    price: 2500,
    duration: '7 days',
    location: 'Tanzania',
    destinations: ['Serengeti', 'Ngorongoro'],
    activities: ['Game Drives', 'Wildlife Viewing'],
    accommodations: ['Safari Lodge'],
    maxGroupSize: 12,
    minGroupSize: 2,
    difficulty: 'moderate' as const,
    includes: ['Accommodation', 'Meals', 'Guide'],
    excludes: ['Flights', 'Visa'],
    images: ['sample-image.jpg'],
    featured: false,
    status: 'active' as const,
    rating: 4.5,
    reviewCount: 10,
    tourType,
    seasonality: {
      greenSeason: true,
      drySeason: true,
      bestMonths: ['June', 'July', 'August']
    },
    createdAt: new Date() as any,
    updatedAt: new Date() as any
  };

  // Customize based on tour type
  switch (tourType) {
    case 'kilimanjaro':
      return {
        ...baseTour,
        title: 'Mount Kilimanjaro Climbing Adventure',
        description: 'Climb the highest peak in Africa with experienced guides',
        destinations: ['Mount Kilimanjaro'],
        activities: ['Mountain Climbing', 'Trekking', 'Summit Attempt'],
        difficulty: 'challenging' as const
      };
    case 'cultural':
      return {
        ...baseTour,
        title: 'Maasai Cultural Immersion Experience',
        description: 'Experience authentic Maasai culture and traditions',
        destinations: ['Maasai Villages', 'Cultural Sites'],
        activities: ['Cultural Visits', 'Traditional Ceremonies', 'Local Crafts']
      };
    case 'wildlife':
    default:
      return baseTour;
  }
}

// Function to run verification tests
export function runTourTypeVerificationTests(): { passed: number; failed: number; results: string[] } {
  const results: string[] = [];
  let passed = 0;
  let failed = 0;

  // Test 1: Valid tour types
  VALID_TOUR_TYPES.forEach(tourType => {
    if (isValidTourType(tourType)) {
      results.push(`✅ Valid tour type test passed: ${tourType}`);
      passed++;
    } else {
      results.push(`❌ Valid tour type test failed: ${tourType}`);
      failed++;
    }
  });

  // Test 2: Invalid tour types
  const invalidTypes = ['standard', 'luxury', 'budget', 'ultra', 'invalid'];
  invalidTypes.forEach(tourType => {
    if (!isValidTourType(tourType)) {
      results.push(`✅ Invalid tour type test passed: ${tourType} correctly rejected`);
      passed++;
    } else {
      results.push(`❌ Invalid tour type test failed: ${tourType} incorrectly accepted`);
      failed++;
    }
  });

  // Test 3: Sample tour creation
  VALID_TOUR_TYPES.forEach(tourType => {
    try {
      const sampleTour = createSampleTour(tourType);
      const validation = validateTour(sampleTour);
      if (validation.isValid) {
        results.push(`✅ Sample tour creation test passed: ${tourType}`);
        passed++;
      } else {
        results.push(`❌ Sample tour creation test failed: ${tourType} - ${validation.errors.join(', ')}`);
        failed++;
      }
    } catch (error) {
      results.push(`❌ Sample tour creation test failed: ${tourType} - ${error}`);
      failed++;
    }
  });

  return { passed, failed, results };
}

// Export for console testing
export function logVerificationResults(): void {
  console.log('🔍 Running Tour Type Verification Tests...\n');
  const { passed, failed, results } = runTourTypeVerificationTests();
  
  results.forEach(result => console.log(result));
  
  console.log(`\n📊 Test Results: ${passed} passed, ${failed} failed`);
  
  if (failed === 0) {
    console.log('🎉 All tour type verification tests passed!');
  } else {
    console.log('⚠️ Some tests failed. Please review the tour type system.');
  }
}
