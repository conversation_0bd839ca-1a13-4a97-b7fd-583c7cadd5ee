
import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import PageLoader from '@/components/ui/PageLoader';
import AdvancedFilters from '@/components/features/AdvancedFilters';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Search, 
  MapPin, 
  Clock, 
  Users, 
  Star, 
  Filter,
  Grid,
  List,
  Camera,
  Bird,
  Leaf,
  Heart,
  Calendar,
  Eye,
  Mountain
} from 'lucide-react';
import { FirebaseService } from '@/services/firebase';
import { Tour, SearchFilters } from '@/types/firebase';

const EnhancedTours = () => {
  const [tours, setTours] = useState<Tour[]>([]);
  const [filteredTours, setFilteredTours] = useState<Tour[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<SearchFilters>({});
  const [sortBy, setSortBy] = useState<string>('featured');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    fetchTours();
  }, []);

  useEffect(() => {
    applyFiltersAndSearch();
  }, [tours, searchTerm, filters, sortBy]);

  const fetchTours = async () => {
    try {
      setLoading(true);
      const toursData = await FirebaseService.getTours();
      setTours(toursData);
    } catch (error) {
      console.error('Error fetching tours:', error);
    } finally {
      setLoading(false);
    }
  };

  const applyFiltersAndSearch = async () => {
    try {
      let filtered = await FirebaseService.searchTours(searchTerm, filters);
      
      // Apply sorting
      switch (sortBy) {
        case 'price-low':
          filtered.sort((a, b) => a.price - b.price);
          break;
        case 'price-high':
          filtered.sort((a, b) => b.price - a.price);
          break;
        case 'rating':
          filtered.sort((a, b) => b.rating - a.rating);
          break;
        case 'duration':
          filtered.sort((a, b) => {
            const aDays = parseInt(a.duration.split(' ')[0]);
            const bDays = parseInt(b.duration.split(' ')[0]);
            return aDays - bDays;
          });
          break;
        case 'featured':
        default:
          filtered.sort((a, b) => {
            if (a.featured && !b.featured) return -1;
            if (!a.featured && b.featured) return 1;
            return b.rating - a.rating;
          });
          break;
      }
      
      setFilteredTours(filtered);
    } catch (error) {
      console.error('Error applying filters:', error);
    }
  };

  const clearFilters = () => {
    setFilters({});
    setSearchTerm('');
  };

  const getTourTypeIcon = (tourType: string) => {
    switch (tourType) {
      case 'wildlife': return Eye;
      case 'kilimanjaro': return Mountain;
      case 'cultural': return Users;
      default: return Eye;
    }
  };

  const getTourTypeColor = (tourType: string) => {
    switch (tourType) {
      case 'wildlife': return 'bg-green-100 text-green-800';
      case 'kilimanjaro': return 'bg-blue-100 text-blue-800';
      case 'cultural': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const renderTourCard = (tour: Tour) => (
    <Card key={tour.id} className="group hover:shadow-xl transition-all duration-300 overflow-hidden">
      <div className="relative">
        <img
          src={tour.images[0]}
          alt={tour.title}
          className={`w-full object-cover group-hover:scale-105 transition-transform duration-300 ${
            viewMode === 'grid' ? 'h-48' : 'h-32'
          }`}
        />
        
        {/* Badges */}
        <div className="absolute top-3 left-3 flex flex-col gap-2">
          {tour.featured && (
            <Badge className="bg-red-500 text-white">Featured</Badge>
          )}
          <Badge className={getTourTypeColor(tour.tourType)}>
            {React.createElement(getTourTypeIcon(tour.tourType), { className: "h-3 w-3 mr-1" })}
            {tour.tourType.charAt(0).toUpperCase() + tour.tourType.slice(1).replace('-', ' ')}
          </Badge>
        </div>

        {/* Wishlist Button */}
        <Button
          variant="ghost"
          size="icon"
          className="absolute top-3 right-3 bg-white/80 hover:bg-white"
        >
          <Heart className="h-4 w-4" />
        </Button>

        {/* Seasonal Badge */}
        <div className="absolute bottom-3 right-3">
          {tour.seasonality?.drySeason && tour.seasonality?.greenSeason ? (
            <Badge variant="outline" className="bg-white/90">Year Round</Badge>
          ) : tour.seasonality?.drySeason ? (
            <Badge variant="outline" className="bg-white/90">Dry Season</Badge>
          ) : tour.seasonality?.greenSeason ? (
            <Badge variant="outline" className="bg-white/90">Green Season</Badge>
          ) : null}
        </div>
      </div>

      <CardContent className={`p-${viewMode === 'grid' ? '6' : '4'}`}>
        <div className={`${viewMode === 'list' ? 'flex items-start justify-between' : ''}`}>
          <div className={`${viewMode === 'list' ? 'flex-1 pr-4' : ''}`}>
            <div className="flex items-start justify-between mb-2">
              <h3 className={`font-bold group-hover:text-orange-600 transition-colors ${
                viewMode === 'grid' ? 'text-xl' : 'text-lg'
              }`}>
                {tour.title}
              </h3>
              {viewMode === 'grid' && (
                <div className="text-right">
                  <div className="text-2xl font-bold text-orange-600">${tour.price.toLocaleString()}</div>
                  <div className="text-sm text-gray-500">per person</div>
                </div>
              )}
            </div>

            <p className={`text-gray-600 mb-4 ${viewMode === 'list' ? 'text-sm' : ''}`}>
              {tour.description.length > 120 ? `${tour.description.slice(0, 120)}...` : tour.description}
            </p>

            {/* Tour Details */}
            <div className={`grid gap-3 mb-4 ${viewMode === 'grid' ? 'grid-cols-2' : 'grid-cols-4'}`}>
              <div className="flex items-center text-sm text-gray-600">
                <Clock className="h-4 w-4 mr-2" />
                <span>{tour.duration}</span>
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <Users className="h-4 w-4 mr-2" />
                <span>Max {tour.maxGroupSize}</span>
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <MapPin className="h-4 w-4 mr-2" />
                <span>{tour.location}</span>
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <Star className="h-4 w-4 mr-2 text-yellow-500" />
                <span>{tour.rating} ({tour.reviewCount})</span>
              </div>
            </div>

            {/* Difficulty and Fitness */}
            {viewMode === 'grid' && (
              <div className="flex items-center gap-3 mb-4">
                <Badge variant="outline" className="capitalize">
                  {tour.difficulty}
                </Badge>
                {tour.fitnessRequirements && (
                  <Badge variant="outline" className="capitalize">
                    {tour.fitnessRequirements.level} fitness
                  </Badge>
                )}
              </div>
            )}

            {/* Special Features */}
            {viewMode === 'grid' && tour.specialFeatures && (
              <div className="flex flex-wrap gap-1 mb-4">
                {tour.specialFeatures.slice(0, 3).map((feature, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {feature}
                  </Badge>
                ))}
                {tour.specialFeatures.length > 3 && (
                  <Badge variant="secondary" className="text-xs">
                    +{tour.specialFeatures.length - 3} more
                  </Badge>
                )}
              </div>
            )}
          </div>

          {/* List View Price and Actions */}
          {viewMode === 'list' && (
            <div className="text-right flex flex-col items-end">
              <div className="text-xl font-bold text-orange-600 mb-2">
                ${tour.price.toLocaleString()}
              </div>
              <div className="text-sm text-gray-500 mb-3">per person</div>
              <div className="flex flex-col gap-2">
                <Button asChild size="sm">
                  <Link to={`/tours/${tour.id}`}>View Details</Link>
                </Button>
                <Button asChild variant="outline" size="sm">
                  <Link to={`/book/${tour.id}`}>Book Now</Link>
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Grid View Actions */}
        {viewMode === 'grid' && (
          <div className="flex gap-2">
            <Button asChild className="flex-1">
              <Link to={`/tours/${tour.id}`}>
                View Details
              </Link>
            </Button>
            <Button asChild variant="outline">
              <Link to={`/book/${tour.id}`}>
                <Calendar className="h-4 w-4 mr-2" />
                Book
              </Link>
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <PageLoader
        title="Loading Enhanced Tours..."
        subtitle="Discovering premium safari experiences for you..."
      />
    );
  }

  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-16">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-safari-sunset to-safari-deep-rust text-primary-foreground py-16">
          <div className="container mx-auto px-4">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">Safari Tours</h1>
            <p className="text-xl max-w-2xl">
              Discover Tanzania's incredible wildlife with our expert-guided safari experiences
            </p>
          </div>
        </div>

        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Filters Sidebar */}
            <div className={`lg:w-80 ${showFilters ? 'block' : 'hidden lg:block'}`}>
              <AdvancedFilters
                filters={filters}
                onFiltersChange={setFilters}
                onClear={clearFilters}
              />
            </div>

            {/* Main Content */}
            <div className="flex-1">
              {/* Search and Controls */}
              <div className="flex flex-col md:flex-row gap-4 mb-6">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search tours, destinations, activities..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={() => setShowFilters(!showFilters)}
                    className="lg:hidden"
                  >
                    <Filter className="h-4 w-4 mr-2" />
                    Filters
                  </Button>
                  
                  <Select value={sortBy} onValueChange={setSortBy}>
                    <SelectTrigger className="w-48">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="featured">Featured First</SelectItem>
                      <SelectItem value="price-low">Price: Low to High</SelectItem>
                      <SelectItem value="price-high">Price: High to Low</SelectItem>
                      <SelectItem value="rating">Highest Rated</SelectItem>
                      <SelectItem value="duration">Duration</SelectItem>
                    </SelectContent>
                  </Select>

                  <div className="flex border rounded-lg">
                    <Button
                      variant={viewMode === 'grid' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setViewMode('grid')}
                      className="rounded-r-none"
                    >
                      <Grid className="h-4 w-4" />
                    </Button>
                    <Button
                      variant={viewMode === 'list' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setViewMode('list')}
                      className="rounded-l-none"
                    >
                      <List className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>

              {/* Results Summary */}
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">
                    {filteredTours.length} Tours Found
                  </h2>
                  {Object.keys(filters).length > 0 && (
                    <p className="text-gray-600">
                      {filteredTours.length} of {tours.length} tours match your criteria
                    </p>
                  )}
                </div>
              </div>

              {/* Tour Listings */}
              {filteredTours.length > 0 ? (
                <div className={`grid gap-6 ${
                  viewMode === 'grid' 
                    ? 'grid-cols-1 md:grid-cols-2 xl:grid-cols-3' 
                    : 'grid-cols-1'
                }`}>
                  {filteredTours.map(renderTourCard)}
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                    <Search className="h-8 w-8 text-gray-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">No tours found</h3>
                  <p className="text-gray-600 mb-4">
                    Try adjusting your search criteria or clearing filters
                  </p>
                  <Button onClick={clearFilters} variant="outline">
                    Clear All Filters
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default EnhancedTours;
