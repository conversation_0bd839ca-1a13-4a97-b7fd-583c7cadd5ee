import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Loader2, AlertTriangle } from 'lucide-react';
import { FirebaseService } from '@/services/firebase';
import { Tour } from '@/types/firebase';

const FALLBACK_IMAGE =
  'https://images.unsplash.com/photo-1516426122078-c23e76319801?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80';

const TourScrollSection = () => {
  const navigate = useNavigate();
  const [tours, setTours] = useState<Tour[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch tours from Firebase
  useEffect(() => {
    const fetchTours = async () => {
      try {
        setLoading(true);
        const allToursData = await FirebaseService.getTours();

        const allTours = allToursData.map((tour: any) => ({
          id: tour.id,
          title: tour.title || 'Untitled Tour',
          description: tour.description || 'No description available',
          price: tour.price || 0,
          duration: tour.duration || 'Duration not specified',
          location: tour.location || '',
          destinations: Array.isArray(tour.destinations) ? tour.destinations : [],
          activities: Array.isArray(tour.activities) ? tour.activities : [],
          accommodations: Array.isArray(tour.accommodations) ? tour.accommodations : [],
          maxGroupSize: tour.maxGroupSize || 12,
          minGroupSize: tour.minGroupSize || 1,
          difficulty: tour.difficulty || 'easy',
          includes: Array.isArray(tour.includes) ? tour.includes : [],
          excludes: Array.isArray(tour.excludes) ? tour.excludes : [],
          images: Array.isArray(tour.images) ? tour.images : [],
          featured: tour.featured || false,
          status: tour.status || 'active',
          rating: tour.rating || 0,
          reviewCount: tour.reviewCount || 0,
          tourType: tour.tourType || 'wildlife',
          category: tour.category || 'Safari',
          accommodationLevel: tour.accommodationLevel || 'Standard',
          seasonality: tour.seasonality || {
            greenSeason: true,
            drySeason: true,
            bestMonths: []
          },
          itinerary: Array.isArray(tour.itinerary) ? tour.itinerary : [],
          fitnessRequirements: tour.fitnessRequirements || {
            level: 'Moderate',
            description: 'Basic fitness required',
            walkingDistance: '2-3 km daily',
            terrain: 'Mostly flat with some uneven ground',
            ageRestrictions: 'Suitable for ages 8+',
            medicalConditions: []
          },
          equipment: {
            provided: [],
            recommended: [],
            required: []
          },
          groupOptions: Array.isArray(tour.groupOptions) ? tour.groupOptions : [],
          specialFeatures: Array.isArray(tour.specialFeatures) ? tour.specialFeatures : [],
          difficultyDetails: tour.difficultyDetails || 'Standard difficulty level',
          createdAt: tour.createdAt || new Date(),
          updatedAt: tour.updatedAt || new Date()
        })) as Tour[];

        let activeTours = allTours.filter(t => t.status === 'active').slice(0, 8);

        if (activeTours.length === 0) {
          // Sample data from codeOne (you can keep your original sample data if preferred)
          activeTours = [
            {
                id: 'sample-1',
                title: 'Serengeti Safari Adventure',
                description: 'Delve into the safari that takes you through Tanzania\'s most famous national parks, offering unparalleled wildlife viewing, including the Great Migration and Big Five encounters.',
                price: 2850,
                duration: '7 DAYS',
                location: 'Serengeti National Park, Tanzania',
                destinations: ['Serengeti', 'Ngorongoro Crater'],
                activities: ['Wildebeest Migration', 'Big Five', 'Luxury Lodges'],
                accommodations: ['Safari Lodge', 'Luxury Tented Camp'],
                maxGroupSize: 12,
                minGroupSize: 2,
                difficulty: 'moderate' as const,
                includes: ['Accommodation', 'All Meals', 'Park Fees', 'Professional Guide'],
                excludes: ['International Flights', 'Visa Fees', 'Personal Expenses'],
                images: ['https://images.unsplash.com/photo-1551632436-cbf8dd35adfa?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80'],
                featured: false,
                status: 'active' as const,
                rating: 4.8,
                reviewCount: 124,
                tourType: 'wildlife' as const,
                category: 'Wildlife Safari',
                accommodationLevel: 'Luxury',
                seasonality: { greenSeason: true, drySeason: true, bestMonths: ['June', 'July', 'August', 'September', 'October'] },
                itinerary: [],
                fitnessRequirements: { level: 'Moderate', description: 'Basic fitness required', walkingDistance: '2-3 km daily', terrain: 'Mostly flat with some uneven ground', ageRestrictions: 'Suitable for ages 8+', medicalConditions: [] },
                equipment: { provided: [], recommended: [], required: [] },
                groupOptions: [],
                specialFeatures: ['Wildebeest Migration', 'Big Five', 'Luxury Lodges'],
                difficultyDetails: 'Moderate physical activity with game drives and short walks',
                createdAt: new Date() as any,
                updatedAt: new Date() as any
              },
              // ... more sample items
          ] as Tour[];
        }

        setTours(activeTours);
      } catch (err) {
        console.error('Error fetching tours:', err);
        setError('Failed to load tours');
      } finally {
        setLoading(false);
      }
    };

    fetchTours();
  }, []);

  return (
    <section className="relative w-full bg-[#F5F1EB]">
      <div className="w-full min-h-screen">
        <div className="grid grid-cols-1 md:grid-cols-[45%_55%] items-start">
          <aside className="flex justify-center items-center px-8 py-16 md:px-[5vw] md:py-0 md:sticky md:top-0 md:h-screen">
            <div className="w-full max-w-[450px] text-center md:text-left">
              <span className="block font-['Montserrat',sans-serif] text-xs tracking-[0.2em] font-bold text-[#4B4237] opacity-80 uppercase mb-4">
                Unforgettable Journeys
              </span>
              <h1 className="font-['Cormorant_Garamond'] text-[clamp(2.2rem,4vw,3.2rem)] leading-[1.2] my-6 text-[#3C352D] font-normal">
                Our Classic Safaris,<br />
                Timeless Journeys,<br />
                <em className="opacity-70">Masterfully Curated.</em>
              </h1>
              <p className="text-sm mb-8 leading-relaxed text-[#4B4237] max-w-[40ch] mx-auto md:mx-0">
                Imagine Africa, perfectly planned. Our expert team meticulously crafts classic safari tours,
                ensuring every detail is seamless. These tried and tested itineraries mean you simply enjoy
                a luxurious, worry-free adventure.
              </p>
              <button
                onClick={() => navigate('/tours')}
                className="inline-block bg-[#B95E2D] text-[#F5F1EB] font-['Montserrat',sans-serif] font-bold text-xs tracking-[0.15em] py-4 px-8 uppercase transition-all duration-300 hover:bg-[#a05126] hover:scale-105 border-none cursor-pointer shadow-lg hover:shadow-xl"
              >
                Explore More
              </button>
            </div>
          </aside>

          <section className="w-full">
            <div className="py-8 md:py-10">
              {loading ? (
                <div className="flex justify-center items-center h-64">
                  <Loader2 className="h-8 w-8 animate-spin text-[#B95E2D]" />
                </div>
              ) : error ? (
                <div className="flex flex-col items-center justify-center h-64 text-center px-8">
                  <AlertTriangle className="h-12 w-12 text-[#B95E2D] mb-4" />
                  <p className="text-[#4B4237] text-sm">{error}</p>
                </div>
              ) : (
                <div className="flex flex-col items-center gap-8">
                  {tours.map((tour) => (
                    <article
                      key={tour.id}
                      className="w-[90%] md:w-4/5 max-w-[400px] mx-auto shadow-[0_10px_30px_rgba(0,0,0,0.05)] overflow-hidden rounded-lg transition-all duration-500 ease-out hover:shadow-[0_20px_40px_rgba(0,0,0,0.1)] hover:scale-[1.02] group"
                    >
                      <div className="relative overflow-hidden">
                        <img
                          src={tour.images?.[0] || FALLBACK_IMAGE}
                          alt={tour.title}
                          className="w-full h-[250px] object-cover block transition-transform duration-700 ease-out group-hover:scale-110"
                          draggable={false}
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = FALLBACK_IMAGE;
                          }}
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        <div className="absolute top-4 right-4 bg-[#B95E2D] text-[#F5F1EB] px-3 py-1 rounded-full transform transition-all duration-300 group-hover:scale-110 group-hover:bg-[#a05126]">
                          <span className="font-['Montserrat',sans-serif] text-xs font-bold">
                            ${tour.price.toLocaleString()} pp
                          </span>
                        </div>
                        {tour.rating > 0 && (
                          <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm text-[#3C352D] px-2 py-1 rounded-full flex items-center gap-1">
                            <span className="text-yellow-400 text-sm">★</span>
                            <span className="font-['Montserrat',sans-serif] text-xs font-bold">
                              {tour.rating}
                            </span>
                          </div>
                        )}
                      </div>

                      <div className="bg-[#EAE3D6] p-6 relative">
                        <span className="font-sans text-[0.65rem] tracking-[0.15em] font-bold text-[#4B4237] opacity-80 block mb-2 uppercase">
                          {tour.duration}
                        </span>
                        <h2
                          className="font-['Cormorant_Garamond'] text-2xl leading-[1.3] text-[#3C352D] mb-3 font-normal cursor-pointer hover:text-[#B95E2D] transition-colors duration-300"
                          onClick={() => navigate(`/tours/${tour.id}`)}
                        >
                          {tour.title}
                        </h2>
                        <p className="font-sans text-xs text-[#4B4237] opacity-70 mb-3 flex items-center gap-1">
                          <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                          </svg>
                          {tour.location}
                        </p>
                        <p className="font-sans text-[0.85rem] text-[#4B4237] leading-relaxed mb-4">
                          {tour.description.length > 120
                            ? `${tour.description.substring(0, 120)}...`
                            : tour.description}
                        </p>
                        {!!tour.activities?.length && (
                          <div className="flex flex-wrap gap-2 mt-4">
                            {tour.activities.slice(0, 3).map((activity, activityIndex) => (
                              <span
                                key={activityIndex}
                                className="inline-block bg-[#D4C2A4] text-[#3C352D] px-2 py-1 rounded-full text-xs font-['Montserrat',sans-serif] font-medium transition-colors duration-300 hover:bg-[#B95E2D] hover:text-[#F5F1EB]"
                              >
                                {activity}
                              </span>
                            ))}
                            {tour.activities.length > 3 && (
                              <span className="inline-block bg-[#4B4237] text-[#F5F1EB] px-2 py-1 rounded-full text-xs font-['Montserrat',sans-serif] font-medium">
                                +{tour.activities.length - 3} more
                              </span>
                            )}
                          </div>
                        )}
                        <div className="flex justify-between items-center mt-4 pt-4 border-t border-[#D4C2A4]/50">
                          <div className="flex items-center gap-4 text-xs text-[#4B4237] opacity-70">
                            <span className="flex items-center gap-1">
                              <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                              </svg>
                              Max {tour.maxGroupSize}
                            </span>
                            <span className="flex items-center gap-1">
                              <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                              </svg>
                              {tour.difficulty}
                            </span>
                          </div>
                          <button
                            onClick={() => navigate(`/tours/${tour.id}`)}
                            className="text-[#B95E2D] hover:text-[#a05126] text-sm font-medium transition-colors duration-300 flex items-center gap-1 group/btn"
                          >
                            View Details
                            <svg className="w-3 h-3 transition-transform duration-300 group-hover/btn:translate-x-1" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                            </svg>
                          </button>
                        </div>
                      </div>
                    </article>
                  ))}
                </div>
              )}
            </div>
          </section>
        </div>
      </div>
    </section>
  );
};

export default TourScrollSection;