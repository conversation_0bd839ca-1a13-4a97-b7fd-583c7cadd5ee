import { useEffect, useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import {
  trackPageView,
  trackEvent,
  trackUserInteraction,
  trackBusinessEvent,
  trackSafariEvent,
  trackFormSubmission,
  trackDownload,
  trackExternalLink,
  trackSearch,
  trackError,
  trackSafariView,
  trackBeginCheckout,
  trackLead,
  isAnalyticsAvailable,
  type AnalyticsEvent,
  type PageViewData
} from '../utils/analytics';

// Custom hook for Google Analytics integration
export function useAnalytics() {
  const location = useLocation();

  // Track page views on route changes
  useEffect(() => {
    if (isAnalyticsAvailable()) {
      trackPageView({
        page_path: location.pathname,
        page_location: window.location.href,
        page_title: document.title,
      });
    }
  }, [location]);

  // Memoized tracking functions
  const track = useCallback((event: AnalyticsEvent) => {
    trackEvent(event);
  }, []);

  const trackInteraction = useCallback((action: string, element: string, details?: Record<string, any>) => {
    trackUserInteraction(action, element, details);
  }, []);

  const trackBusiness = useCallback((action: string, details?: Record<string, any>) => {
    trackBusinessEvent(action, details);
  }, []);

  const trackSafari = useCallback((action: string, safari_type?: string, details?: Record<string, any>) => {
    trackSafariEvent(action, safari_type, details);
  }, []);

  const trackForm = useCallback((form_name: string, success: boolean, details?: Record<string, any>) => {
    trackFormSubmission(form_name, success, details);
  }, []);

  const trackFileDownload = useCallback((file_name: string, file_type?: string) => {
    trackDownload(file_name, file_type);
  }, []);

  const trackExternal = useCallback((url: string, link_text?: string) => {
    trackExternalLink(url, link_text);
  }, []);

  const trackSearchQuery = useCallback((search_term: string, results_count?: number) => {
    trackSearch(search_term, results_count);
  }, []);

  const trackAppError = useCallback((error_message: string, error_type: string, page?: string) => {
    trackError(error_message, error_type, page);
  }, []);

  const trackSafariPackageView = useCallback((safari_id: string, safari_name: string, price?: number) => {
    trackSafariView(safari_id, safari_name, price);
  }, []);

  const trackBookingStart = useCallback((safari_id: string, safari_name: string, value: number) => {
    trackBeginCheckout(safari_id, safari_name, value);
  }, []);

  const trackLeadGeneration = useCallback((lead_type: string, safari_interest?: string, value?: number) => {
    trackLead(lead_type, safari_interest, value);
  }, []);

  const trackCustomPageView = useCallback((data: PageViewData) => {
    trackPageView(data);
  }, []);

  return {
    // Core tracking functions
    track,
    trackPageView: trackCustomPageView,
    
    // Specific event tracking
    trackInteraction,
    trackBusiness,
    trackSafari,
    trackForm,
    trackFileDownload,
    trackExternal,
    trackSearchQuery,
    trackAppError,
    
    // Ecommerce tracking
    trackSafariPackageView,
    trackBookingStart,
    trackLeadGeneration,
    
    // Utility
    isAvailable: isAnalyticsAvailable,
  };
}

// Hook for tracking component mount/unmount
export function usePageTracking(pageName: string, category?: string) {
  const { track } = useAnalytics();

  useEffect(() => {
    // Track page/component view
    track({
      action: 'page_view',
      category: category || 'Page',
      label: pageName,
    });

    // Track time on page
    const startTime = Date.now();

    return () => {
      const timeOnPage = Date.now() - startTime;
      track({
        action: 'time_on_page',
        category: category || 'Page',
        label: pageName,
        value: Math.round(timeOnPage / 1000), // Convert to seconds
      });
    };
  }, [pageName, category, track]);
}

// Hook for tracking form interactions
export function useFormTracking(formName: string) {
  const { trackForm, trackInteraction } = useAnalytics();

  const trackFormStart = useCallback(() => {
    trackInteraction('form_start', formName);
  }, [formName, trackInteraction]);

  const trackFormSubmit = useCallback((success: boolean, details?: Record<string, any>) => {
    trackForm(formName, success, details);
  }, [formName, trackForm]);

  const trackFieldInteraction = useCallback((fieldName: string, action: string = 'focus') => {
    trackInteraction(`form_field_${action}`, `${formName}_${fieldName}`);
  }, [formName, trackInteraction]);

  return {
    trackFormStart,
    trackFormSubmit,
    trackFieldInteraction,
  };
}

// Hook for tracking safari package interactions
export function useSafariTracking() {
  const { trackSafari, trackSafariPackageView, trackBookingStart, trackLeadGeneration } = useAnalytics();

  const trackPackageView = useCallback((safari_id: string, safari_name: string, price?: number) => {
    trackSafariPackageView(safari_id, safari_name, price);
  }, [trackSafariPackageView]);

  const trackPackageInteraction = useCallback((action: string, safari_type: string, details?: Record<string, any>) => {
    trackSafari(action, safari_type, details);
  }, [trackSafari]);

  const trackBookingInitiation = useCallback((safari_id: string, safari_name: string, value: number) => {
    trackBookingStart(safari_id, safari_name, value);
  }, [trackBookingStart]);

  const trackInquiry = useCallback((safari_interest: string, inquiry_type: string = 'general', value?: number) => {
    trackLeadGeneration(inquiry_type, safari_interest, value);
  }, [trackLeadGeneration]);

  return {
    trackPackageView,
    trackPackageInteraction,
    trackBookingInitiation,
    trackInquiry,
  };
}
